<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Email</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"],
        textarea {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="file"] {
            margin-top: 10px;
        }
        input[type="submit"] {
            margin-top: 20px;
            width: 100%;
            padding: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 20px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f4f4f4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Send Email</h1>
        <form id="emailForm" enctype="multipart/form-data">
            <label for="senderName">Sender Name:</label>
            <input type="text" id="senderName" name="senderName" required>

            <label for="orderId">Order IDx:</label>
            <input type="text" id="orderId" name="orderId" required>
            
            <label for="to">To (One email and name per line, separated by a comma):</label>
            <textarea id="to" name="to" rows="10" required></textarea>
            
            <label for="subject">Subject:</label>
            <input type="text" id="subject" name="subject" required>
            
            <label for="body">Message (use {name} to insert recipient's name):</label>
            <textarea id="body" name="body" rows="10" cols="30" required></textarea>
            
            <label for="attachment">Attachment:</label>
            <input type="file" id="attachment" name="attachment">
            
            <input type="submit" value="Send Email">
        </form>
        <div class="status" id="status"></div>
        <div class="result-table" id="resultTableContainer" style="display:none;">
            <h2>Emails Sent Report</h2>
            <table id="resultTable">
                <thead>
                    <tr>
                        <th>Email</th>
                        <th>Status</th>
						<th>Time Sent er</th> <!-- New column for time sent -->                    </tr>
                </thead>
                <tbody></tbody>
            </table>
            <button id="downloadCSV">Download CSV (Visible Emails)</button>
            <button id="downloadAnonymizedCSV">Download CSV (Anonymized Emails)</button>
        </div>
    </div>
    
    <script>
        document.getElementById('emailForm').addEventListener('submit', function(e) {
            e.preventDefault();
            var formData = new FormData(this);
            var recipients = formData.get('to').split('\n').map(function(line) {
                var parts = line.split(',');
                return { email: parts[0].trim(), name: parts[1] ? parts[1].trim() : '' }; // Handle no name provided
            });
            formData.delete('to');
            document.getElementById('status').innerHTML = 'Sending emails...';

            var results = [];
            var emailsProcessed = 0;

            recipients.forEach(function(recipient, index) {
                setTimeout(function() {
                    var emailData = new FormData();
                    for (var pair of formData.entries()) {
                        emailData.append(pair[0], pair[1]);
                    }
                    emailData.append('to', recipient.email);
                    emailData.append('name', recipient.name);
                    emailData.append('subject', formData.get('subject').replace('{name}', recipient.name)); // Replace {name} in subject
                    emailData.append('body', formData.get('body').replace('{name}', recipient.name)); // Replace {name} in body
                    sendEmail(emailData, recipient.email, function(result) {
                        results.push(result);
                        emailsProcessed++;
                        if (emailsProcessed === recipients.length) {
                            displayResults(results);
                        }
                    });
                }, index * 2000); // Adjust the interval if necessary
            });
        });
		////
		function sendEmail(data, email, callback) {
			var xhr = new XMLHttpRequest();
			xhr.open('POST', 'send_email.php', true);
			xhr.onload = function() {
				var status = document.getElementById('status');
				var timeSent = new Date().toLocaleString(); // Get current time when the response is received
				if (xhr.status === 200) {
					callback({ email: email, status: xhr.responseText, timeSent: timeSent }); // Attach the timeSent
				} else {
					callback({ email: email, status: 'Failed', timeSent: timeSent }); // Handle failure and still include timeSent
				}
			};
			xhr.send(data);
		}

		function displayResults(results) {
			var tableContainer = document.getElementById('resultTableContainer');
			var tableBody = document.getElementById('resultTable').getElementsByTagName('tbody')[0];
			tableBody.innerHTML = '';

			results.forEach(function(result) {
				var row = tableBody.insertRow();
				var cellEmail = row.insertCell(0);
				var cellStatus = row.insertCell(1);
				var cellTimeSent = row.insertCell(2); // New cell for time sent
				cellEmail.textContent = result.email;
				cellStatus.textContent = result.status;
				cellTimeSent.textContent = result.timeSent; // Display the time sent from the response
			});

			tableContainer.style.display = 'block';
			document.getElementById('status').innerHTML = 'Emails processing complete!';

			document.getElementById('downloadCSV').addEventListener('click', function() {
				downloadCSV(results);
			});

			document.getElementById('downloadAnonymizedCSV').addEventListener('click', function() {
				downloadAnonymizedCSV(results);
			});
		}

		function downloadCSV(results) {
			var orderId = document.getElementById('orderId').value;
			var csvContent = "data:text/csv;charset=utf-8,Email,Status,Time Sent\n"; // Include Time Sent in CSV
			results.forEach(function(result) {
				var row = result.email + "," + result.status + "," + result.timeSent; // Include Time Sent in row
				csvContent += row + "\n";
			});

			var encodedUri = encodeURI(csvContent);
			var link = document.createElement("a");
			link.setAttribute("href", encodedUri);
			link.setAttribute("download", orderId + "_emails_sent_report.csv");
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}

		function downloadAnonymizedCSV(results) {
			var orderId = document.getElementById('orderId').value;
			var csvContent = "data:text/csv;charset=utf-8,Email,Status,Time Sent\n"; // Include Time Sent in CSV
			results.forEach(function(result) {
				var anonymizedEmail = anonymizeEmail(result.email);
				var row = anonymizedEmail + "," + result.status + "," + result.timeSent; // Include Time Sent in row
				csvContent += row + "\n";
			});

			var encodedUri = encodeURI(csvContent);
			var link = document.createElement("a");
			link.setAttribute("href", encodedUri);
			link.setAttribute("download", orderId + "_emails_sent_report_anonymized.csv");
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		}
    </script>
</body>
</html>
