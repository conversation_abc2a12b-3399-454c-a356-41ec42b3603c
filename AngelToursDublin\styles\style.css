/*
CSS Credit: http://www.justonedesigner.com/
*/

body {
	margin: 0;
	padding: 0;
	line-height: 1.5em;
	font-family: Helvetica, Arial, sans-serif;
	font-size: 12px;
	background:#d58049;
	color: #333333;
}

/* Getting the new tags to behave */

h2 {
	margin: 0 0 10px 0;
	padding: 5px 0 5px 10px;
	font-size: 16px;
	color: #ffffff;
}


h3 {
	letter-spacing: 5px;
	margin: 0;
	padding: 3px 0 3px 10px;
	font-size: 16px;
	font-weight: bold;
	color: #ffffff;
}

h5 {
	margin: 0 0 10px 0;
	padding: 0;
	font-size: 16px;
	font-weight: bold;
	color: #333;
}

.title {
	margin: 0 0 5px 0;
	padding: 0;
	font-size: 16px;
	font-weight: bold;
	color: #00923F;
}

p{
	margin: 0 0 15px 0;
}

img {
	border: none;
	margin: 0;
}

li {list-style:none;}

a {text-decoration:none;}

#container {
	margin: auto;
	width: 1000px;
	background:url(../images/bg.gif);
    -moz-box-shadow:0px 0px 10px #222;
    -webkit-box-shadow:0px 0px 10px #222;
    box-shadow:0px 0px 10px #222;
}

/* top panel */
#top_panel {
	height: 140px;
	background:url(../images/logo.png)  20px no-repeat;}

#header {
	float: left;
	display: inline;
	margin: 10px 0 0 10px;
}

#header #site_title{
	padding: 10px 0 10px 0;
	margin: 0;
	color: #036067;
	font-size: 40px;
	font-weight: bold;
	background: none;
}

#header #slogan{
	padding-left: 190px;
	margin-top: 50px;
	color: #222222;
	font-size: 12px;
	font-weight: bold;
}

.picture {
	float: right;
	color: #FFFFFF;
	margin: 40px 15px 0 0;
	width: 468px;
	height: 60px;
}

/* end of top panel */

#clearDiv {clear:both;}

/*-----------------------contents------------------------*/
#content {
	margin:0px 0px 0px 10px;
	font-size:14px;
	padding:0px 0px 0px 10px;
	background:#ffffff;
}

/*-----------------------contact------------------------*/
.form_contact{
width:930px;
background-color:#dddddd;
border: 1px solid #cccccc;
float:left;
text-align:left;
color:#615357;
padding:10px;
margin:0px 0px 20px 0px;
}

.location_contact{
font-size:14px;
width:250px;
float:left;
padding:15px;
}

input {
border: 1px solid #C8D2D9;
font-size:20px;
background-color:#FFFFFF;
margin:0px;
width:250px;
height:26px;
margin-top:10px;
margin-left:20px;
}
select{
border: 1px solid #C8D2D9;
font-size:20px;
background-color:#FFFFFF;
margin:0px;
width:250px;
height:26px;
margin-top:10px;
margin-left:20px;
}
textarea{
border: 1px solid #C8D2D9;
background-color:#FFFFFF;
margin:0px;
width:300px;
height:80px;
float:left;
margin-top:10px;
margin-left:20px;
}

label.left{
float:left;
width:70px;
font-size:20px;
padding-top:3px;
text-align:right;
}

.botton {background:url(../images/booknow.png) no-repeat;
		 height:41px;
		 width:167px;
	     img-border:none;}

#currentdate {color:#ffffff;
		 font-family:sans;
		 font-size:14px;
		 position:absolute;
		 right:140px;
		 background-color:#000000;
		 opacity:0.6;
		  margin:10px 5px 0px 0px;}
	
