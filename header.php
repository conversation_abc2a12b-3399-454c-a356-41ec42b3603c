<!-- Favicon -->
    <link rel="icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
	<?php
	// For Production
	echo '<base href="https://myyquiz.com/">';
	// For Local Host
    //echo '<link rel="stylesheet" href="all.min.css">' . "\n";
	//echo '<script src="3.4.16.js"></script>' . "\n";
	?>

<!-- Critical CSS to prevent CLS -->
<style>
/* Critical styles loaded immediately to prevent layout shifts */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
}

/* Navbar critical styles */
nav {
    position: sticky;
    top: 0;
    z-index: 50;
    background-color: white;
    border-bottom: 1px solid #e5e7eb;
    height: 64px;
    contain: layout style;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo area */
.navbar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
    text-decoration: none;
    min-width: 120px;
}

/* Navigation links */
.navbar-nav {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-link {
    color: #4b5563;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
}

.navbar-link:hover {
    color: #2563eb;
}

/* Mobile menu button */
.mobile-menu-button {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }

    .mobile-menu-button {
        display: block;
    }
}

/* Footer critical styles */
footer {
    background-color: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: auto;
}

/* Prevent flash of unstyled content */
.loading {
    visibility: hidden;
}

.loaded {
    visibility: visible;
}
</style>

<!-- Load Tailwind CSS with defer for better performance -->
<script src="https://cdn.tailwindcss.com" defer></script>

<!-- Performance optimizations -->
<link rel="preconnect" href="https://www.googletagmanager.com">
<link rel="preconnect" href="https://cdnjs.cloudflare.com">
<link rel="preconnect" href="https://cdn.tailwindcss.com">
<link rel="preconnect" href="https://pagead2.googlesyndication.com">
<link rel="preconnect" href="https://us-assets.i.posthog.com">

<!-- Preload and async load Font Awesome -->
<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></noscript>
	
	<?php
	// <link rel="stylesheet" href="dark-mode.css?v=11">
	//echo '<base href="https://myyquiz.com/">';
	// For Local Host
	?>
    <meta name="keywords" content="موقع ماي كويز,لينك ماي كويز,اختبار,تحليل شخصية,اختبار لهجه,ماي كويز">

    <!-- Performance optimization meta tags -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#ffffff">

<!-- Google Tag Manager - Optimized for performance -->
<script>
// Defer GTM loading to reduce main thread blocking
window.addEventListener('load', function() {
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-M8LNXC9');
});
</script>
<!-- End Google Tag Manager -->


<!-- Google tag (gtag.js) - Optimized loading -->
<script>
// Defer Google Analytics to improve performance
window.addEventListener('load', function() {
    var script = document.createElement('script');
    script.async = true;
    script.src = 'https://www.googletagmanager.com/gtag/js?id=G-3LBN0G1R2J';
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-3LBN0G1R2J');
});
</script>

<?php /*
<!-- Hotjar Tracking Code for https://myyquiz.com -->
<script>
    (function(h,o,t,j,a,r){
        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
        h._hjSettings={hjid:5103254,hjsv:6};
        a=o.getElementsByTagName('head')[0];
        r=o.createElement('script');r.async=1;
        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
        a.appendChild(r);
    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
</script>


<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "qhf4cjs0jl");
</script>
*/ ?>
<!-- PostHog - Deferred loading for better performance -->
<script>
window.addEventListener('load', function() {
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init Ie Ts Ms Ee Es Rs capture Ge calculateEventProperties Os register register_once register_for_session unregister unregister_for_session js getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Ds Fs createPersonProfile Ls Ps opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Cs debug I As getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_TZwLrdGTHZYiMAKxwWcCSLpgSAuMeXc48OlR7fdKKmw', {
        api_host: 'https://us.i.posthog.com',
        defaults: '2025-05-24',
        person_profiles: 'identified_only'
    });
});
</script>

<!-- AdSense - Deferred loading for better performance -->
<script>
window.addEventListener('load', function() {
    var adsScript = document.createElement('script');
    adsScript.async = true;
    adsScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-8688285054527401';
    adsScript.crossOrigin = 'anonymous';
    document.head.appendChild(adsScript);
});
</script>

<!-- CLS Prevention Script -->
<script>
// Prevent layout shifts by ensuring stable dimensions
document.addEventListener('DOMContentLoaded', function() {
    // Mark body as loaded to prevent FOUC
    document.body.classList.add('loaded');

    // Ensure all images have stable dimensions
    const images = document.querySelectorAll('img');
    images.forEach(function(img) {
        if (!img.style.aspectRatio && img.width && img.height) {
            img.style.aspectRatio = img.width + '/' + img.height;
        }

        // Add loading placeholder
        if (!img.complete) {
            img.style.backgroundColor = '#f3f4f6';
        }

        img.addEventListener('load', function() {
            this.style.backgroundColor = 'transparent';
        });
    });

    // Optimize font loading
    if ('fonts' in document) {
        document.fonts.ready.then(function() {
            document.body.classList.add('fonts-loaded');
        });
    }
});

// Intersection Observer for lazy loading optimization
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                const img = entry.target;
                if (img.dataset.src) {
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            }
        });
    }, {
        rootMargin: '50px 0px'
    });

    document.addEventListener('DOMContentLoaded', function() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(function(img) {
            imageObserver.observe(img);
        });
    });
}
</script>