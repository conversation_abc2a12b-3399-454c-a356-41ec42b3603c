<?php
/* Database credentials. Assuming you are running MySQL
server with default setting (user 'root' with no password) */


define('DB_SERVER', '193.203.168.99');
define('DB_USERNAME', 'u105319521_cvinfinity');
define('DB_PASSWORD', '0555058890ASMa');
define('DB_NAME', 'u105319521_cvinfinity');


/* Attempt to connect to MySQL database */
$link = mysqli_connect(DB_SERVER, DB_USERNAME, DB_PASSWORD, DB_NAME);
//mysqli_set_charset('utf8', $link);

// Set charset to utf8mb4
if (!$link->set_charset("utf8mb4")) {
    die("Error loading character set utf8mb4: " . $link->error);
}

// Check connection
if($link === false){
    die("ERROR: Could not connect. ");
    //die("ERROR: Could not connect. " . mysqli_connect_error());
}

?>