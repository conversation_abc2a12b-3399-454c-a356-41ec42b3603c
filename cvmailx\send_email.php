<?php
use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $senderName = htmlspecialchars(trim($_POST['senderName']));
    $to = filter_var(trim($_POST['to']), FILTER_VALIDATE_EMAIL);
    $name = htmlspecialchars(trim($_POST['name']));
    $orderId = htmlspecialchars(trim($_POST['orderId']));
    $subject = str_replace('{name}', $name, $_POST['subject']);
    $body = str_replace('{name}', $name, $_POST['body']);
    $attachment = $_FILES['attachment'];

    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = 'smtp.hostinger.com';       // Set Hostinger SMTP server
        $mail->SMTPAuth   = true;
        $mail->Username   = '<EMAIL>';     // Your Hostinger email address
        $mail->Password   = '0555058890ASMa!';            // Your Hostinger email password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = 587;

        // Recipients
        $mail->setFrom('<EMAIL>', $senderName);
        $mail->addAddress($to);

        // Attachments
        if ($attachment['error'] == UPLOAD_ERR_OK) {
            $mail->addAttachment($attachment['tmp_name'], $attachment['name']);
        }

        // Content
        $mail->isHTML(true);
        $mail->CharSet = 'UTF-8';  // Support for Arabic
        $mail->Subject = $subject;
        //$mail->Body    = $body . '<img src="https://myyquiz.com/cvmailx/track_open.php?email=' . urlencode($to) . '" width="1" height="1" alt=""/>';
        $mail->Body    = $body . '<img src="https://myyquiz.com/cvmailx/faq.php?email=' . urlencode($to) . '&orderId=' . urlencode($orderId) . '" width="1" height="1" alt=""/>';
        $mail->AltBody = strip_tags($body);

        // Send email
        if (!$mail->send()) {
            echo "Mailer Error: " . $mail->ErrorInfo;
        } else {
            echo 'Email has been sent';
        }
    } catch (Exception $e) {
        echo "Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
    }
}
?>