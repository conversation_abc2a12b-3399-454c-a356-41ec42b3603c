<?php
require_once 'db.php';

try {
    // Get and validate quiz ID
    $quizId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if ($quizId < 1) {
		error_log($quizId);
        header("Location: /");
        exit();
    }
	
	
    // Single query to get quiz details and verify existence
    $stmt = $pdo->prepare("
        SELECT title, description, image, type, created_at, 
               category_id, subcategory_id 
        FROM quizzes 
        WHERE id = ? 
        LIMIT 1
    ");
    $stmt->execute([$quizId]);
    
    if (!$quizData = $stmt->fetch(PDO::FETCH_ASSOC)) {
		error_log($quizId);
        header("Location: /");
        exit();
    }

    // Extract quiz details
    [
        'title' => $quizTitle,
        'description' => $quizDescr,
        'image' => $quizImage,
        'type' => $quizType,
        'created_at' => $quizDate,
        'category_id' => $quizCate,
        'subcategory_id' => $quizScat
    ] = $quizData;

    // Process image URL
    $quizImageHQ = str_replace('/imagesindex/', '/imageshq/', $quizImage);
    $quizImageHQFulink = 'https://' . $_SERVER['HTTP_HOST'] . '/' . htmlspecialchars($quizImageHQ);

    // Single query for questions and options
    $stmt = $pdo->prepare("
        SELECT q.id AS question_id, q.text AS question_text,
               o.id AS option_id, o.text AS option_text
        FROM questions q
        LEFT JOIN options o ON q.id = o.question_id
        WHERE q.quiz_id = ?
        ORDER BY q.id
    ");
    $stmt->execute([$quizId]);
    
    // Process questions in single pass
    $questions = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $qid = $row['question_id'];
        
        if (!isset($questions[$qid])) {
            $questions[$qid] = [
                'id' => $qid,
                'text' => $row['question_text'],
                'options' => []
            ];
        }
        
        if ($row['option_id']) {
            $questions[$qid]['options'][] = [
                'id' => $row['option_id'],
                'text' => $row['option_text']
            ];
        }
    }

    // Convert to indexed array
    $questions = array_values($questions);

} catch(PDOException $e) {
	error_log($quizId);
    error_log("DB Error: " . $e->getMessage());
    header("Location: /");
    exit();
}
// Add translation function
function translate($en, $ar, $isRTL) {
    return $isRTL ? $ar : $en;
}

// Detect language function
function isRTL($text) {
    return !preg_match('/^[A-Za-z]/', $text);
}


// Fetch a larger pool of random quizzes
$poolQuery = $pdo->query("
    SELECT title, id, description, image
    FROM quizzes
    ORDER BY RAND()
    LIMIT 30
");
$quizPool = $poolQuery->fetchAll();

$latestQuizzes = [];
foreach ($quizPool as $quiz) {
    $addQuiz = true;
    $currentTitle = strtolower($quiz['title']);
    
    // Compare with already selected quizzes
    foreach ($latestQuizzes as $existingQuiz) {
        $existingTitle = strtolower($existingQuiz['title']);
        similar_text($currentTitle, $existingTitle, $similarity);
        
        if ($similarity >= 50) {
            $addQuiz = false;
            break;
        }
    }
    
    // Add if meets similarity requirement
    if ($addQuiz) {
        $latestQuizzes[] = $quiz;
        // Stop when we reach 20 quizzes
        if (count($latestQuizzes) >= 20) {
            break;
        }
    }
}
$base_url = 'https://' . $_SERVER['HTTP_HOST'] . '/';
//echo $base_url;

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $quizTitle; ?></title>
    <style>
	

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

        }

        .quiz-container {
            max-width: 95%;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
        }

        .quiz-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .quiz-title {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .progress-bar {
            height: 4px;
            background: #eee;
            border-radius: 2px;
            margin: 1rem 0;
        }

        .progress {
            height: 100%;
            background: #3498db;
            width: 30%;
            transition: width 0.3s ease;
        }

        .question-card { display: none; }
        .question-card.active { display: block; }
		
		.question-card[style] {
			display: block !important;
		}
		
        .active-question {
            display: block !important;
        }

        .selected-option {
            border-color: #3498db !important;
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.1);
        }
		
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .rtl .answer-options {
            text-align: right;
        }

        .rtl .answer-option {
            padding: 1rem 1.5rem 1rem 0.5rem;
        }

		
        .question-number {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .question-text {
            color: #2c3e50;
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
        }

        .answer-options {
            display: grid;
            gap: 1rem;
        }

        .answer-option {
            padding: 1rem;
            border: 2px solid #eee;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .answer-option:hover {
            transform: translateY(-2px);
            border-color: #3498db;
            box-shadow: 0 5px 15px rgba(52,152,219,0.1);
        }

        .next-btn {
            display: block;
            margin: 1rem auto 0;
            padding: 0.8rem 2rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .next-btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52,152,219,0.2);
        }

        .score-container {
            text-align: center;
            padding: 2rem;
        }

        .score-text {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

		
		.social-btn i {
		font-size: 1.2rem;
		}

		.social-btn:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
			transition: all 0.3s ease;
		}

		.social-buttons {
			display: flex;
			justify-content: center;
			gap: 1rem;
			margin-bottom: 2rem;
			flex-wrap: wrap;
			padding: 0 1rem;
		}
		
		.social-btn {
			color: white;
			padding: 0.8rem 1.5rem;
			border-radius: 25px;
			text-decoration: none;
			display: flex;
			align-items: center;
			gap: 0.5rem;
			justify-content: center;
			width: 100%;
			max-width: 250px;
		}

		@media (min-width: 768px) {
			.social-btn {
				width: auto;
			}
			.social-buttons {
				flex-wrap: nowrap;
				padding: 0;
			}
		}
	
		.footer-link a:hover {
			text-decoration: underline;
			color: #2980b9;
		}
		
		@keyframes fadeOut {
			0% { opacity: 1; }
			80% { opacity: 1; }
			100% { opacity: 0; }
		}

		#copyLinkBtn:hover {
			transform: translateY(-2px);
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
			transition: all 0.3s ease;
		}
		.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e3e3e3 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-color: transparent !important;
    min-height: 48px;
    max-height: 26px !important; /* same height as normal answer-option */
    border-radius: 8px;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

		#emojiContainer {
			position: fixed;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 9999;
			background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent background */
			color: white; /* Emoji color */
			padding: 20px;
			border-radius: 10px;
			text-align: center;
		}
		.lazyload {
			opacity: 0;
			transition: opacity 0.3s ease-in;
			background: #f3f4f6;
			min-height: 200px;
			object-fit: cover;
		}

		.lazyload.loaded {
			opacity: 1;
			background: none;
		}
        #quiz-section{
            padding-top: 4rem;
    padding-bottom: 4rem;
        }
        .padding-y-20{
            padding: 80px 0px;
        }
    </style>


	<?php
	$colors = ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFC0CB', '#FFFF00', '#EE82EE'];
	$randomColor = $colors[array_rand($colors)];
	?>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo htmlspecialchars($quizDescr); ?>">
    <meta name="keywords" content="موقع ماي كويز,لينك ماي كويز,اختبار,تحليل شخصية,اختبار لهجه,ماي كويز">
    <meta name="author" content="YouName">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlspecialchars($base_url); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($quizTitle); ?>">
    <meta property="og:site_name" content="ماي كويز">
    <meta property="og:description" content="<?php echo htmlspecialchars($quizDescr); ?>">
    <meta property="og:image" content="<?php echo htmlspecialchars($quizImageHQFulink); ?>">
	<meta name="theme-color" content="<?php echo $randomColor; ?>">

	
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo htmlspecialchars($base_url); ?>">
    <meta property="twitter:title" content="<?php echo htmlspecialchars($quizTitle); ?>">
    <meta property="twitter:description" content="<?php echo htmlspecialchars($quizDescr); ?>">
    <meta property="twitter:image" content="<?php echo htmlspecialchars($quizImageHQFulink); ?>">
    <meta name="twitter:site" content="@MyyQuizCom">
	<meta name="twitter:image:alt" content="ماي كويز Myyquiz">

	<?php include 'header.php'; ?>
	
	<!-- Confetti JS -->
	<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.4.0/dist/confetti.browser.min.js" defer></script>
</head>
    <!-- Navbar -->
	<?php include 'navbar.php'; ?>

    <!-- Hero Section -->
	<?php 
	$skipHero = 1;
	$heroImg = "";
	$heroImgAlt = "";
	$heroMainText = "ماي كويز | Myyquiz";
	$heroSubText = $quizTitle;
	
	
	// In your config/header.php
	$heroConfig = [
		'left' => [
			'link' => '/make.php',
			'text' => 'صمم اختبارك ✨',
			'enabled' => true
		],
		'right' => [
			'link' => '#',
			'text' => '⬇️',
			'enabled' => false
		]
	];

	include 'hero.php'; 
	?>

	<br>

<body class="bg-gray-50">
    <?php if (!empty($questions)): ?>
	<section id="quiz-section" class="">
	<div class="quiz-container">
		<div class="quiz-header">

		  <style>
    /* بسيط للتنسيق */
    #breadcrumb {
      font-size: 14px;
      margin: 1em 0;
    }
    #breadcrumb a {
      text-decoration: none;
      color: #007bff;
      margin: 0 0.2em;
    }
    #breadcrumb span {
      margin: 0 0.2em;
      color: #555;
    }
	
  </style>
  
<?php
$categories = [
    0  => ['title' => 'جميع الاختبارات', 'url' => 'all.php'],
    1  => ['title' => 'اختبارات اللغة الإنجليزية (STEP)', 'url' => 'englishexams.php'],
    2  => ['title' => 'اختبارات القدرات', 'url' => 'qudratsexams.php'],
    3  => ['title' => 'اختبارات دينية', 'url' => 'religionsexams.php'],
    4  => ['title' => 'اختبارات ثقافية', 'url' => 'culturalexams.php'],
    5  => ['title' => 'اختبارات تحليل الشخصية', 'url' => 'personality.php'],
    6  => ['title' => 'اختبارات اللهجات', 'url' => 'lhgat.php'],
    7  => ['title' => 'اختبارات أكاديمية ومهنية', 'url' => 'academicexams.php'],
    8  => ['title' => 'اختبار انمي', 'url' => 'animes.php'],
    9  => ['title' => 'اختبار افلام ومسلسلات', 'url' => 'movies.php'],
    10 => ['title' => 'اختبارات عامة ومتنوعة', 'url' => 'generalexams.php'],
];

$currentCategory = $categories[$quizCate] ?? $categories[0]; // fallback
?>
  
			  <nav id="breadcrumb"style="text-align: center;">
				<a href="/">الرئيسية</a>
				<span>&gt;</span>
				<a href="<?php echo $currentCategory['url']; ?>">
					<?php echo $currentCategory['title']; ?>
				</a>
				<span>&gt;&gt;</span>
				<span id="current-title"></span>
			  </nav>


			<!-- Added quiz meta information -->
			<h1 class="quiz-title"><?= $quizTitle; ?></h1>
			
			<!-- Added quiz meta information -->
			<div class="quiz-meta">
				<?php if(isset($quizDate)): ?>
					<span class="creation-date">التاريخ: <?= date('Y-m-d', strtotime($quizDate)); ?></span>
				<?php endif; ?>
			</div>

			<!-- Added quiz description -->
			<?php if(!empty($quizDescr)): ?>
				<div class="quiz-description">
					<?= $quizDescr; ?>
				</div>
			<?php endif; ?>

			<div class="progress-bar">
				<div class="progress"></div>
			</div>
		</div>

    <div id="skeleton-container">

            <div class="question-number skeleton" style="width:150px;height:16px;"></div>
            <div class="question-text skeleton" style="width:80%;height:24px;margin-top:10px;"></div>
            <div class="answer-options">
                <div class=" skeleton" style="height:26px;"></div>
                <div class=" skeleton" style="height:26px;"></div>
                <div class=" skeleton" style="height:26px;"></div>
       

           
        </div>
    </div>

        <?php foreach ($questions as $index => $question): 
            $isRTL = isRTL($question['text']);
        ?>
        <div class="question-card <?= $isRTL ? 'rtl' : '' ?>" 
             data-question-id="<?= $question['id'] ?>">
            <div class="question-number">
                <?= translate(
                    "Question ".($index+1)." of ".count($questions),
                    "السؤال ".($index+1)." من ".count($questions),
                    $isRTL
                ) ?>
            </div>
            <h2 class="question-text"><?= htmlspecialchars($question['text']) ?></h2>
        <div class="answer-options">

                    <?php foreach ($question['options'] as $option): ?>
                        <div class="answer-option <?= isRTL($option['text']) ? 'rtl' : '' ?>" 
                             data-option-id="<?= $option['id'] ?>">
                            <?= htmlspecialchars($option['text']) ?>
                        </div>
                    <?php endforeach; ?>

            </div>
        </div>
        <?php endforeach; ?>

        <button class="next-btn"><?= translate('Next Question →', '← التالي', $isRTL) ?></button>
    </div>
	</section>
    <?php endif; ?>

	<!-- Modified Score Container -->
	<div class="score-container" id="score-container" style="display: none;">
		<!-- Text from response -->
		
		<span id="emojiContainer" style="display: none; font-size: 10em;">👎</span>
		
		<h2 class="tttit" style="font-size: 1.8rem; margin-bottom: 1.5rem;"><?= $quizTitle; ?></h2>
		<h2 class="score-main-text" style="font-size: 1.8rem; margin-bottom: 1.5rem;"></h2>

		<!-- Image with underText as alt -->
		<img class="result-image" src="" alt="" style="margin: 0 auto 1rem; display: block;">
		
		<!-- UnderText from response -->
		<p class="under-text" style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;"></p>
		
		<!-- UnderText from response -->
		<p class="quiz-link" style="display: none;"></p>

		<!-- Share Section -->
		<div class="share-section" style="text-align: center; margin: 2rem 0;">
			<!-- Decorative line -->
			<div style="position: relative; margin: 2rem 0;">
				<div style="height: 2px; background: #eee;"></div>
				<div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: white; padding: 0 1rem;">
					<span style="color: #3498db; font-size: 1.5rem;">✨</span>
				</div>
			</div>

			<p style="font-size: 1.2rem; color: #666; margin-bottom: 1.5rem;">
				الآن قم بمشاركة النتيجة مع اصدقائك لتعرفهم اكثر ✨
			</p>

			<!-- Social Media Buttons -->
			<div class="social-buttons">
				<a target="_blank" href="#" rel="noopener noreferrer" class="social-btn facebook" style="background: #1877f2; color: white; padding: 0.8rem 1.5rem; border-radius: 25px; text-decoration: none; display: flex; align-items: center; gap: 0.5rem; width: 100%; max-width: 250px; justify-content: center;">
					<i class="fab fa-facebook"></i>
					فيسبوك
				</a>
				<a target="_blank" href="#" rel="noopener noreferrer" class="social-btn twitter" style="background: #1da1f2; color: white; padding: 0.8rem 1.5rem; border-radius: 25px; text-decoration: none; display: flex; align-items: center; gap: 0.5rem; width: 100%; max-width: 250px; justify-content: center;">
					<i class="fab fa-twitter"></i>
					تويتر
				</a>
				<a target="_blank" href="#" rel="noopener noreferrer" class="social-btn whatsapp" style="background: #25d366; color: white; padding: 0.8rem 1.5rem; border-radius: 25px; text-decoration: none; display: flex; align-items: center; gap: 0.5rem; width: 100%; max-width: 250px; justify-content: center;">
					<i class="fab fa-whatsapp"></i>
					واتساب
				</a>
				<a target="_blank" href="#" rel="noopener noreferrer" class="social-btn telegram" style="background: #0088cc; color: white; padding: 0.8rem 1.5rem; border-radius: 25px; text-decoration: none; display: flex; align-items: center; gap: 0.5rem; width: 100%; max-width: 250px; justify-content: center;">
					<i class="fab fa-telegram"></i>
					تليجرام
				</a>
			</div>
			
			<!-- Add this under social buttons -->
			<div class="copy-section" style="margin: 1.5rem 0;">
				<button id="copyLinkBtn" style="
					background: #9b59b6;
					color: white;
					padding: 0.8rem 2rem;
					border: none;
					border-radius: 25px;
					cursor: pointer;
					font-size: 1rem;
					display: flex;
					align-items: center;
					gap: 0.5rem;
					margin: 0 auto;
				">
					<i class="fas fa-copy"></i>
					نسخ رابط النتيجة
				</button>
			</div>
			
			<!-- Pointer Section -->
			<div style="font-size: 1.5rem; margin: 1rem 0;">
				👇
			</div>
		</div>

		<!-- Footer Link -->
		<div class="footer-link" style="text-align: center; margin-top: 2rem;">
			<a href="make.php" style="color: #3498db; text-decoration: none; font-size: 1.1rem;">
				اعجبك الاختبار؟ ودك تصمم اختبار خاص فيك ✨ ؟ اضغط هنا
			</a>
		</div>
	</div>
		
    <!-- Features Section -->
    <section class="padding-y-20">		
		<div class="container mx-auto px-4">
			<h2 class="text-3xl font-bold text-center text-blue-400 mb-12">اخترنا لك</h2>
			<div id="quizzes-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				<div class="text-center py-12">Loading quizzes...</div>
			</div>
		</div>
    </section>

    <!-- Footer -->
	<?php include 'footer.php'; ?>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const quizId = <?= json_encode($quizId) ?>;
            const questionCards = Array.from(document.querySelectorAll('.question-card'));
            const totalQuestions = questionCards.length;
            let currentQuestionIndex = 0;
            let userAnswers = {};

            // Initialize quiz
            function initialize() {
                if (questionCards.length > 0) {
                    document.getElementById('skeleton-container').style.display = 'none';
                    questionCards[0].classList.add('active');
                    updateProgress();
                    updateButtonText();
                }
            }

            // Answer selection
            document.querySelectorAll('.answer-option').forEach(option => {
                option.addEventListener('click', function() {
                    const questionCard = this.closest('.question-card');
                    const questionId = questionCard.dataset.questionId;
                    const optionId = this.dataset.optionId;

                    questionCard.querySelectorAll('.answer-option').forEach(opt => {
                        opt.classList.remove('selected-option');
                    });
                    this.classList.add('selected-option');
                    userAnswers[questionId] = optionId;
                });
            });

            // Next button handler
            document.querySelector('.next-btn').addEventListener('click', async (e) => {
                e.preventDefault();
				
                document.querySelector('.creation-date').style.display = 'none';
				document.querySelector('.quiz-description').style.display = 'none';
				//document.getElementById('quiz-section').scrollIntoView({ behavior: 'smooth' });
				
                const currentCard = questionCards[currentQuestionIndex];
                const questionId = currentCard.dataset.questionId;

                if (!userAnswers[questionId]) {
                    alert('نسيت تختار اجابة');
                    return;
                }

                if (currentQuestionIndex < totalQuestions - 1) {
                    currentCard.classList.remove('active');
                    currentQuestionIndex++;
                    questionCards[currentQuestionIndex].classList.add('active');
                    updateProgress();
                    updateButtonText();
                } else {
                    await submitQuiz();
                }
            });

            function updateProgress() {
                const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;
                document.querySelector('.progress').style.width = `${progress}%`;
            }

            function updateButtonText() {
                const nextBtn = document.querySelector('.next-btn');
                const isRTL = questionCards[currentQuestionIndex].classList.contains('rtl');
                nextBtn.textContent = currentQuestionIndex === totalQuestions - 1 
                    ? (isRTL ? 'عرض النتيجة' : 'Submit Answers') 
                    : (isRTL ? 'التالي ←' : 'Next Question →');
            }

            async function submitQuiz() {
                const formData = new URLSearchParams();
                formData.append('quiz_id', quizId);

                for (const [qId, oId] of Object.entries(userAnswers)) {
                    formData.append(`answers[${qId}]`, oId);
                }

                try {
                    const response = await fetch('process_neo.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                        body: formData
                    });

                    const result = await response.json();
                    document.querySelector('.quiz-container').style.display = 'none';
                    document.querySelector('.score-container').style.display = 'block';
					document.getElementById('score-container').scrollIntoView({ behavior: 'smooth' });
                    // Update score display with result data
					

					document.querySelector('.score-main-text').textContent = result.text;
					//document.querySelector('.result-image').src = result.image;
					document.querySelector('.result-image').alt = result.underText;
					document.querySelector('.under-text').textContent = result.underText;
					
					
					document.querySelector('.quiz-link').textContent = result.quizLink;
					const quizImageHQ = "<?php echo $quizImageHQ; ?>";

					// Your result object
					if (result.image === "uploads/imagesresults/default_good.png") {
						document.querySelector('.result-image').src = quizImageHQ;
					} else {
						document.querySelector('.result-image').src = result.image;
					}
					
					// Setup social sharing
					const quizLink = encodeURIComponent(result.quizLink);
					const quizTitle = encodeURIComponent('<?= $quizTitle ?>');

					document.querySelectorAll('.social-btn').forEach(btn => {
						const platform = btn.classList[1];
						btn.href = {
							facebook: `https://www.facebook.com/sharer/sharer.php?u=${quizLink}`,
							twitter: `https://twitter.com/intent/tweet?text=${quizTitle}&url=${quizLink}`,
							whatsapp: `https://api.whatsapp.com/send?text=${quizTitle} ${quizLink}`,
							telegram: `https://t.me/share/url?url=${quizLink}&text=${quizTitle}`
						}[platform];
					});

					if (result.score > 70) {
						realistic();
					}


					if (result.score > 1 && result.score < 50) {
						const emojiContainer = document.getElementById('emojiContainer');
						
						// Fade in
						emojiContainer.style.display = 'block'; // or appropriate display value
						emojiContainer.animate([{ opacity: 0 }, { opacity: 1 }], { duration: 400 })
							.finished
							.then(() => {
								// Wait 1 second
								return new Promise(resolve => setTimeout(resolve, 1000));
							})
							.then(() => {
								// Fade out
								return emojiContainer.animate([{ opacity: 1 }, { opacity: 0 }], { duration: 400 }).finished;
							})
							.then(() => {
								emojiContainer.style.display = 'none';
							});
					}

                } catch (error) {
                    console.error('Submission failed:', error);
                    alert('Error submitting quiz');
                }
            }
			
			

            initialize();
        });
    </script>
	
    <script>
    function realistic() {
      var count = 200;
      var defaults = {
        origin: { y: 0.7 }
      };

      function fire(particleRatio, opts) {
        confetti({
          ...defaults,
          ...opts,
          particleCount: Math.floor(count * particleRatio)
        });
      }

      fire(0.25, {
        spread: 26,
        startVelocity: 55,
      });
      fire(0.2, {
        spread: 60,
      });
      fire(0.35, {
        spread: 100,
        decay: 0.91,
        scalar: 0.8
      });
      fire(0.1, {
        spread: 120,
        startVelocity: 25,
        decay: 0.92,
        scalar: 1.2
      });
      fire(0.1, {
        spread: 120,
        startVelocity: 45,
      });
    }
	
	</script>
	<script>
	document.getElementById('copyLinkBtn').addEventListener('click', async () => {
    try {
        const link = document.querySelector('.quiz-link').textContent;
        await navigator.clipboard.writeText(link);
        
        // Show message
        const msg = document.createElement('div');
        msg.textContent = 'تم النسخ! وترا حلوة ذي، سو لصق في السوشل ميديا وشف كيف النتيجة تطلع ✨';
        msg.style.position = 'fixed';
        msg.style.bottom = '20px';
        msg.style.left = '50%';
        msg.style.transform = 'translateX(-50%)';
        msg.style.background = '#2ecc71';
        msg.style.color = 'white';
        msg.style.padding = '1rem 2rem';
        msg.style.borderRadius = '25px';
        msg.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
        msg.style.zIndex = '1000';
        msg.style.animation = 'fadeOut 3s forwards';
        
        document.body.appendChild(msg);
        
        // Remove message after animation
        setTimeout(() => {
            msg.remove();
        }, 3000);
        
    } catch (err) {
        alert('حدث خطأ أثناء نسخ الرابط، حاول مرة أخرى');
        console.error('Copy failed:', err);
    }
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quizzesContainer = document.getElementById('quizzes-container');
    const quizTitle = "<?= addslashes($quizTitle); ?>";

    // عرض حالة التحميل
    quizzesContainer.innerHTML = `
        <div class="col-span-3 text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-blue-500 border-t-transparent"></div>
        </div>
    `;

    fetch(`get_quizzes.php?category=${encodeURIComponent(<?= $quizCate; ?>)}&subcategory=${encodeURIComponent(<?= $quizScat; ?>)}`)
        .then(response => response.json())
        .then(quizzes => {
            let html = '';

            // أولًا: إضافة العنصر الثابت (ID = 999) في بداية القائمة
            html += `
                <a href="quiz/581" class="block">
                    <div class="bg-white rounded-lg shadow-md p-8 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-lg">
                        <!-- قم بتعديل مسار الصورة والعنوان بحسب ما تريده للكويز 999 -->
                        <img 
                            src="uploads/imageshq/file_6841a3b0b780b9.64946991.jpg" 
                            alt="اختبار ردود العيد"
                            width="416"
                            height="233"
                            loading="lazy"
                            class="h-auto max-w-full rounded-lg m-auto"
                        >
                        <h3 class="text-xl font-semibold mb-3">اختبار ردود العيد</h3>
                        <span class="border-2 border-green-500 text-green-500 font-semibold py-2 px-20 rounded-lg hover:bg-green-500 hover:text-white transition-all duration-300 inline-block">
                            إبدأ
                        </span>
                    </div>
                </a>
            `;

            // ثانيًا: إنشاء العناصر الديناميكية القادمة من الخادم
            quizzes.forEach(quiz => {
                html += `
                    <a href="quiz/${quiz.id}" class="block">
                        <div class="bg-white rounded-lg shadow-md p-8 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-lg">
                            <img 
                                src="${quiz.image}"
                                alt="${quiz.title}"
                                width="416"
                                height="233"
                                loading="lazy"
                                class="h-auto max-w-full rounded-lg m-auto"
                            >
                            <h3 class="text-xl font-semibold mb-3">${quiz.title}</h3>
                            <span class="border-2 border-green-500 text-green-500 font-semibold py-2 px-20 rounded-lg hover:bg-green-500 hover:text-white transition-all duration-300 inline-block">
                                إبدأ
                            </span>
                        </div>
                    </a>
                `;
            });

            // عرض المحتوى النهائي
            quizzesContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Error:', error);
            quizzesContainer.innerHTML = '<div class="col-span-3 text-center text-red-500">Error loading quizzes</div>';
        });
});
</script>
  <script>
    // يضع عنوان الصفحة في البند الأخير من الكرينباد
    document.getElementById('current-title').textContent = document.title;
  </script>

</body>
</html>