<?php include 'config.php'; ?>
<?php

// Check the connection
if ($link->connect_error) {
    error_log("Connection failed: " . $link->connect_error);
    die("Connection failed: " . $link->connect_error);
}

$email = isset($_GET['email']) ? htmlspecialchars($_GET['email']) : '';
$orderId = isset($_GET['orderId']) ? htmlspecialchars($_GET['orderId']) : '';

if (!$email || !$orderId) {
    // If the script is accessed directly without required parameters, exit
    http_response_code(404);
    exit("Not Found");
}

if ($email && $orderId) {
    // Prepare and bind
	$stmt = $link->prepare("INSERT INTO email_tracking (email, order_id, emailstatus) VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 3 HOUR))");
    $stmt->bind_param("ss", $email, $orderId);

    // Execute the statement
    if (!$stmt->execute()) {
        error_log("Error executing statement: " . $stmt->error);
    }

    // Close the statement and connection
    $stmt->close();
}

$link->close();

// Send a 1x1 transparent gif
header('Content-Type: image/gif');
echo base64_decode("R0lGODlhAQABAAAAACwAAAAAAQABAAA=");
?>
