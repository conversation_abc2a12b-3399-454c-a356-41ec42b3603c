<!-- Footer with Inline CSS -->
<style>
/* Footer styles - inline to prevent render blocking */
.footer {
    background: linear-gradient(to right, rgba(37, 99, 235, 0.8), rgba(96, 165, 250, 0.8));
    background-color: #1f2937;
    color: white;
    padding: 3rem 0;
    margin-top: auto;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
}

@media (min-width: 1024px) {
    .footer-container {
        padding: 0 2rem;
    }
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

@media (min-width: 768px) {
    .footer-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.footer-section h3 {
    color: #60a5fa;
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.footer-section p {
    color: #d1d5db;
    line-height: 1.6;
    margin: 0;
}

.footer-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-list li {
    margin-bottom: 0.5rem;
}

.footer-link {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.15s ease-in-out;
}

.footer-link:hover {
    color: #60a5fa;
}

.footer-divider {
    border-top: 1px solid #374151;
    padding-top: 2rem;
    text-align: center;
}

.footer-copyright {
    color: #9ca3af;
    margin: 0;
}

/* External link indicator */
.footer-link[href^="https://"]::after {
    content: " ↗";
    font-size: 0.8em;
    opacity: 0.7;
}
</style>

<footer class="footer">
    <div class="footer-container">
        <div class="footer-grid">
            <div class="footer-section">
                <h3>عن الموقع</h3>
                <p>ماي كويز موقع ترفيهي لإجراء الاختبارات ومشاركة النتائج استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!</p>
            </div>

            <div class="footer-section">
                <h3>روابط سريعة</h3>
                <ul class="footer-list">
                    <li><a href="/" class="footer-link">الرئيسية</a></li>
                    <li><a href="regx.php" class="footer-link">تسجيل حساب</a></li>
                    <li><a href="make.php" class="footer-link">صمم اختبارك ✨</a></li>
                    <li><a href="all.php" class="footer-link">كل الاختبارات</a></li>
                    <li><a href="englishexams.php" class="footer-link">اختبارات اللغة الإنجليزية (STEP)</a></li>
                    <li><a href="qudratsexams.php" class="footer-link">اختبارات القدرات</a></li>
                    <li><a href="contact.html" class="footer-link">اتصل بنا</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>تصنيفات الاختبارات</h3>
                <ul class="footer-list">
                    <li><a href="movies.php" class="footer-link">اختبارات الافلام والمسلسلات</a></li>
                    <li><a href="generalexams.php" class="footer-link">اختبارات عامة ومتنوعة</a></li>
                    <li><a href="culturalexams.php" class="footer-link">اختبارات ثقافية</a></li>
                    <li><a href="religionsexams.php" class="footer-link">اختبارات دينية</a></li>
                    <li><a href="academicexams.php" class="footer-link">اختبارات أكاديمية ومهنية</a></li>
                    <li><a href="https://x.com/MyyQuizCom" class="footer-link" target="_blank" rel="noopener">تويتر (𝕏)</a></li>
                    <li><a href="https://t.me/myyquiz" class="footer-link" target="_blank" rel="noopener">☆ تيليقرام</a></li>
                </ul>
            </div>
        </div>

				<?php /*
                <div>
                    <h3 class="text-blue-400 text-xl font-bold mb-4">اتصل بنا</h3>
                    <ul class="text-gray-300 space-y-2">
                        <li>العنوان: الرياض، السعودية</li>
                    </ul>
                    <div class="flex space-x-4 space-x-reverse mt-4">
                        <a href="https://twitter.com/MyyQuizCom" class="text-white hover:text-blue-400 transition-colors" aria-label="Twitter">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-white hover:text-blue-400 transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-white hover:text-blue-400 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>
				 */?>
				
        </div>

        <div class="footer-divider">
            <p class="footer-copyright">&copy; 2025 جميع الحقوق محفوظة</p>
        </div>
    </div>
</footer>

<!-- Footer Performance Script -->
<script>
(function() {
    'use strict';

    // Optimize external links
    document.addEventListener('DOMContentLoaded', function() {
        const externalLinks = document.querySelectorAll('a[href^="https://"]');
        externalLinks.forEach(function(link) {
            if (!link.hasAttribute('rel')) {
                link.setAttribute('rel', 'noopener noreferrer');
            }
            if (!link.hasAttribute('target')) {
                link.setAttribute('target', '_blank');
            }
        });

        // Add loading indicators for external links
        externalLinks.forEach(function(link) {
            link.addEventListener('click', function() {
                this.style.opacity = '0.7';
                setTimeout(function() {
                    link.style.opacity = '1';
                }, 1000);
            });
        });
    });

    // Performance optimization: Intersection Observer for footer
    if ('IntersectionObserver' in window) {
        const footerObserver = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    // Footer is visible, can trigger any footer-specific actions
                    entry.target.classList.add('footer-visible');
                }
            });
        }, {
            threshold: 0.1
        });

        const footer = document.querySelector('.footer');
        if (footer) {
            footerObserver.observe(footer);
        }
    }
})();
</script>


    <script>
        // Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const nav = document.getElementById('mainNav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-white', 'shadow-md');
                nav.classList.remove('text-white');
                nav.querySelectorAll('a').forEach(link => {
                    link.classList.remove('text-white');
                    link.classList.add('text-gray-800');
                });
            } else {
                nav.classList.remove('bg-white', 'shadow-md');
                nav.classList.add('text-white');
                nav.querySelectorAll('a').forEach(link => {
                    link.classList.add('text-white');
                    link.classList.remove('text-gray-800');
                });
            }
        });
    </script>