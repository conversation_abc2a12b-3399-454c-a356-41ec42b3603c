<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>

        <head>
            <title>This site is under development</title>

				<style type="text/css" media="screen">
					html { color: #333; font-family: "Helvetica Neue", Arial, Helvetica, sans-serif; font-size: 13px; text-align: center; }
					#container { margin: 0 auto; text-align: center; }
				</style>
                <script type="text/javascript" language="JavaScript">
<!-- 
var domainname = window.location.hostname;
var google_afd_request = {
    client: 'ca-dp-oversee_ncd',
    domain_name: domainname,
    referrer: document.referrer,
    session_token: 'create'
};
var param_name = '';
var param_value = '';
var frame;
var drid;

var registrar_frameset = function(params) {
    drid = params['drid'];

    if (params['a_id']) {
        param_name = 'a_id';
    }
    else if (params['o_id']) {
        param_name = 'o_id';
    }
    param_value = params[param_name];
    frame = document.getElementById(params['frame']);

    google_afd_request['drid'] = params['drid'];

    if (!frame) {
        document.write('<title>' + domainname + '</title>\n');
        document.write('<meta name="keywords" content="' + domainname + '">\n');
        document.write('<meta name="description" content="' + domainname + '">\n');
    }

    var token_url = 'http://pagead2.googlesyndication.com/apps/domainpark/show_afd_ads.js';
    document.write('<script type="text/javascript" language="JavaScript" ' +
                   'src="' + token_url + '"></' + 'script>\n');
}

function google_afd_ad_request_done(response) {
    var url = 'http://dsnextgen.com/'
            + '?domainname=' + domainname
            + '&drid=' + drid
            + (param_name ? ('&' + param_name + '=' + param_value) : '')
            + '&session_token=' + response.session_token;
    if (frame) {
        frame.name = domainname;
        frame.src = url;
    }
    else {
                document.write('<div id="container"><h1>This site is under development</h1><p>This page indicates the webmaster has not uploaded a website to the server.</p><p>For information on how to build or upload a site, please visit your web hosting company\'s site.</p></div>');
        document.write('<iframe src="' + url + '" frameborder="0" height="800" scrolling="auto" width="100%"></iframe>');
    }
}
-->
                </script>

                <script type="text/javascript" language="JavaScript">
                          registrar_frameset({a_id: 115165, drid: 'as-drid-2578124767373827'}); 
                </script>
        </head>

</html>

