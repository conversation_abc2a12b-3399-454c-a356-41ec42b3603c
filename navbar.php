<nav id="mainNav" class="main-nav">
  <div class="nav-container">
    
    <!-- Logo with fixed dimensions to prevent CLS -->
    <a href="/" class="logo-link">
      <img src="myyquizlogo4.gif"
           alt="شعار ماي كويز"
           width="150"
           height="33"
           class="logo-img"
           loading="eager"
           decoding="async"
           fetchpriority="high" />
    </a>

    <!-- Hamburger button -->
    <button id="navToggle" class="hamburger-btn" aria-label="Toggle navigation menu">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
           fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>

    <!-- Menu -->
    <ul id="navMenu" class="nav-menu">
      <li><a href="/" class="nav-link">الرئيسية</a></li>
      <li><a href="https://x.com/MyyQuizCom" class="nav-link">تويتر (𝕏)</a></li>
      <li><a href="https://t.me/myyquiz" class="nav-link">☆ تيليقرام</a></li>
    </ul>
  </div>
</nav>

<style>
/* Base styles */
body {
  margin: 0;
}
.main-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 51;
  background-color: transparent;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}
.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.logo-link {
  display: flex;
  align-items: center;
  min-height: 32px;
}
.logo-img {
  display: block;
  height: 33px !important;
}
.hamburger-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: white;
  display: block;
}
.nav-menu {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1.5rem;
}
.nav-menu li {
  display: inline-block;
}
.nav-link {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 16px;
}
.nav-link:hover {
  color: #bfdbfe; /* blue-200 */
}

/* Scroll state */
.scrolled {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.scrolled .nav-link {
  color: #1f2937; /* gray-800 */
}
.scrolled .nav-link:hover {
  color: #2563eb; /* blue-600 */
}
.scrolled .hamburger-btn {
  color: #1f2937;
}

/* Desktop */
@media (min-width: 768px) {
  .hamburger-btn {
    display: none;
  }
  .nav-menu {
    display: flex !important;
    gap: 1.5rem;
  }
}

/* Mobile dropdown */
@media (max-width: 767px) {
  .nav-menu {
    flex-direction: column;
    gap: 1rem;
    background: white;
    padding: 1rem;
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
  }
  .nav-menu .nav-link {
    color: #1f2937;
  }
}
</style>

<script>
const nav   = document.getElementById('mainNav');
const btn   = document.getElementById('navToggle');
const menu  = document.getElementById('navMenu');

// Toggle menu for mobile
btn.addEventListener('click', () => {
  if (menu.style.display === 'flex') {
    menu.style.display = 'none';
  } else {
    menu.style.display = 'flex';
  }
});

// Scroll style change
window.addEventListener('scroll', () => {
  if (window.scrollY > 50) {
    nav.classList.add('scrolled');
  } else {
    nav.classList.remove('scrolled');
  }
});
</script>
