<nav id="mainNav" class="fixed w-full z-50 transition-all duration-300">
  <div class="container mx-auto px-4 lg:px-8 flex justify-between items-center py-4">
    <!-- Logo -->
    <a href="/" class="flex items-center">
      <img src="myyquizlogo4.gif" alt="شعار ماي كويز"   width="25" height="80"  class="h-8 w-auto" />

    </a>

    <!-- Hamburger button (visible on small screens) -->
    <button id="navToggle" class="md:hidden text-white focus:outline-none">
      <svg id="hamburgerIcon" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>

    <!-- Menu (hidden on mobile, flex on md+) -->
    <ul id="navMenu" class="hidden md:flex space-x-6 space-x-reverse text-white">
      <li><a href="/" class="hover:text-blue-200 transition-colors">الرئيسية</a></li>
	  <?php /*
      <li><a href="all.php" class="hover:text-blue-200 transition-colors">كل الاختبارات</a></li>
      <li><a href="lhgat.php" class="hover:text-blue-200 transition-colors">اختبارات اللهجات</a></li>
      <li><a href="personality.php" class="hover:text-blue-200 transition-colors">اختبارات تحليل الشخصية</a></li>
      <li><a href="animes.php" class="hover:text-blue-200 transition-colors">اختبارات الأنمي</a></li>
	  */ ?>
	<li><a href="https://x.com/MyyQuizCom" class="hover:text-blue-200 transition-colors">تويتر (𝕏)</a></li>
	<li><a href="https://t.me/myyquiz" class="hover:text-blue-200 transition-colors">☆ تيليقرام</a></li>
    </ul>
  </div>
</nav>

<script>
// elements
const nav   = document.getElementById('mainNav');
const btn   = document.getElementById('navToggle');
const menu  = document.getElementById('navMenu');
const links = menu.querySelectorAll('a');

// mobile toggle
btn.addEventListener('click', () => {
  menu.classList.toggle('hidden');
});

// scroll listener
window.addEventListener('scroll', () => {
  if (window.scrollY > 50) {
    nav.classList.add('bg-white', 'shadow-lg');
    nav.classList.remove('bg-transparent');
    btn.classList.replace('text-white', 'text-gray-800');
    links.forEach(a => {
      a.classList.replace('text-white', 'text-gray-800');
      a.classList.replace('hover:text-blue-200', 'hover:text-blue-600');
    });
  } else {
    nav.classList.remove('bg-white', 'shadow-lg');
    nav.classList.add('bg-transparent');
    btn.classList.replace('text-gray-800', 'text-white');
    links.forEach(a => {
      a.classList.replace('text-gray-800', 'text-white');
      a.classList.replace('hover:text-blue-600', 'hover:text-blue-200');
    });
  }
});

// init state
nav.classList.add('bg-transparent');
btn.classList.add('text-white');
links.forEach(a => a.classList.add('text-white'));
</script>
