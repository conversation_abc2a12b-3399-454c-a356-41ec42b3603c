<?php

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صمم اختبارك - ماي كويز</title>

    <!-- Performance optimization meta tags -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#2563eb">

    <!-- SEO Meta Tags -->
    <meta name="author" content="YouName">
    <meta name="description" content="صمم اختبارك الخاص مع ماي كويز - اختر بين اختبارات النقاط أو اختبارات تحليل الشخصية بالذكاء الصناعي">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://myyquiz.com/make.php">
    <meta property="og:title" content="صمم اختبارك - ماي كويز">
    <meta property="og:description" content="صمم اختبارك الخاص مع ماي كويز - اختر بين اختبارات النقاط أو اختبارات تحليل الشخصية بالذكاء الصناعي">
	<meta property="og:image" content="https://myyquiz.com/images/bnr.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://myyquiz.com/make.php">
    <meta property="twitter:title" content="صمم اختبارك - ماي كويز">
    <meta property="twitter:description" content="صمم اختبارك الخاص مع ماي كويز - اختر بين اختبارات النقاط أو اختبارات تحليل الشخصية بالذكاء الصناعي">
    <meta property="twitter:image" content="https://myyquiz.com/images/bnr.jpg">
    <meta name="twitter:site" content="@MyyQuizCom">
	<meta name="twitter:image:alt" content="ماي كويز Myyquiz">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "صمم اختبارك - ماي كويز",
        "url": "https://myyquiz.com/make.php",
        "description": "صمم اختبارك الخاص مع ماي كويز - اختر بين اختبارات النقاط أو اختبارات تحليل الشخصية بالذكاء الصناعي"
    }
    </script>

	<meta itemprop="image" content="https://myyquiz.com/images/bnr.jpg">
	<link rel="image_src" href="https://myyquiz.com/images/bnr.jpg" />

	<?php include 'header.php'; ?>

    <!-- Critical CSS for Make Page -->
    <style>
        /* Page-specific styles */
        body {
            background-color: #f9fafb;
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            direction: rtl;
        }

        /* Features section */
        .features-section {
            padding: 5rem 0;
            background-color: #f9fafb;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .features-title {
            font-size: 2.25rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 4rem;
            background: linear-gradient(to right, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            line-height: 1.2;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2.5rem;
        }

        @media (min-width: 768px) {
            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .features-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        /* Feature cards */
        .feature-card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            min-height: 500px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .feature-card:hover {
            transform: scale(1.05);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* Icon container */
        .icon-container {
            width: 5rem;
            height: 5rem;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.875rem;
        }

        .icon-blue {
            background: linear-gradient(135deg, #60a5fa, #3b82f6);
        }

        .icon-purple {
            background: linear-gradient(135deg, #a78bfa, #8b5cf6);
        }

        /* Card content */
        .card-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1f2937;
            line-height: 1.3;
        }

        .card-subtitle {
            color: #6b7280;
            margin-bottom: 1.25rem;
            font-weight: 500;
        }

        /* Examples list */
        .examples-list {
            margin-bottom: 2rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .example-link {
            display: block;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .example-blue {
            background-color: #eff6ff;
        }

        .example-blue:hover {
            background-color: #dbeafe;
            color: #1d4ed8;
        }

        .example-purple {
            background-color: #f3e8ff;
        }

        .example-purple:hover {
            background-color: #e9d5ff;
            color: #7c3aed;
        }

        /* CTA Button */
        .cta-button {
            display: inline-block;
            width: 100%;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
            min-height: 48px;
            line-height: 1.5;
        }

        .cta-blue {
            background: linear-gradient(to right, #3b82f6, #2563eb);
        }

        .cta-blue:hover {
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
        }

        .cta-purple {
            background: linear-gradient(to right, #8b5cf6, #7c3aed);
        }

        .cta-purple:hover {
            box-shadow: 0 10px 15px -3px rgba(139, 92, 246, 0.4);
            transform: translateY(-2px);
        }

        /* Icon styles using Unicode */
        .icon-star::before {
            content: "⭐";
            font-size: 1.2em;
        }

        .icon-robot::before {
            content: "🤖";
            font-size: 1.2em;
        }

        .icon-arrow-left::before {
            content: "←";
            font-size: 1em;
            margin-left: 0.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 767px) {
            .features-title {
                font-size: 2rem;
                margin-bottom: 3rem;
            }

            .feature-card {
                min-height: 450px;
                padding: 1.5rem;
            }

            .features-section {
                padding: 3rem 0;
            }
        }
    </style>
	
</head>
<body>

    <!-- Navbar -->
	<?php include 'navbar.php'; ?>

    <!-- Hero Section -->
	<?php
	$skipHero = 0;
	$heroImg = "";
	$heroImgAlt = "";
	$heroMainText = "صمم اختبارك الخاص | ماي كويز";
	$heroSubText = "اختر نوع الاختبار المناسب لك - اختبارات نقاط أو تحليل شخصية بالذكاء الصناعي ✨";


	// Hero configuration
	$heroConfig = [
		'left' => [
			'link' => '#quiz-types',
			'text' => 'اختر نوع الاختبار 👇',
			'enabled' => true
		],
		'right' => [
			'link' => '/',
			'text' => 'العودة للرئيسية',
			'enabled' => true
		]
	];

	include 'hero.php';
	?>


	<!-- Features Section -->
	<section id="quiz-types" class="features-section">
		<div class="features-container">
			<h2 class="features-title">
				اختر نوع الاختبار
			</h2>

			<div class="features-grid">
				<!-- Feature 1: Score-based Quiz -->
				<div class="feature-card">
					<div>
						<div class="icon-container icon-blue">
							<span class="icon-star" aria-hidden="true"></span>
						</div>
						<h3 class="card-title">اختبار نقاط (نجاح ورسوب)</h3>
						<p class="card-subtitle">أمثلة شائعة:</p>
						<div class="examples-list">
							<a href="https://myyquiz.com/q/369" target="_blank" rel="noopener" class="example-link example-blue">
								اختبار ستيب STEP
							</a>
							<a href="https://myyquiz.com/q/387" target="_blank" rel="noopener" class="example-link example-blue">
								اختبار قدرات
							</a>
							<a href="https://myyquiz.com/id/9" target="_blank" rel="noopener" class="example-link example-blue">
								اختبار اللهجة النجدية
							</a>
						</div>
					</div>
					<a href="qcscore.html" class="cta-button cta-blue" aria-label="إنشاء اختبار نقاط">
						صمم اختبارك الآن <span class="icon-arrow-left" aria-hidden="true"></span>
					</a>
				</div>

				<!-- Feature 2: AI Personality Quiz -->
				<div class="feature-card">
					<div>
						<div class="icon-container icon-purple">
							<span class="icon-robot" aria-hidden="true"></span>
						</div>
						<h3 class="card-title">اختبار تحليل شخصية (ذكاء صناعي)</h3>
						<p class="card-subtitle">أمثلة تفاعلية:</p>
						<div class="examples-list">
							<a href="https://myyquiz.com/id/30" target="_blank" rel="noopener" class="example-link example-purple">
								إلى أين سأسافر في 2030
							</a>
							<a href="https://myyquiz.com/id/29" target="_blank" rel="noopener" class="example-link example-purple">
								ماهي وظيفتي المستقبلية
							</a>
							<a href="https://myyquiz.com/id/19" target="_blank" rel="noopener" class="example-link example-purple">
								شخصيتي من حيواناتي المفضلة
							</a>
						</div>
					</div>
					<a href="qcGPT.html" class="cta-button cta-purple" aria-label="إنشاء اختبار تحليل شخصية">
						صمم اختبارك الآن <span class="icon-arrow-left" aria-hidden="true"></span>
					</a>
				</div>

			</div>
		</div>
	</section>
    <!-- Footer -->
	<?php include 'footer.php'; ?>

    <!-- Performance and CLS Optimization Script -->
    <script>
    (function() {
        'use strict';

        // Smooth scroll for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const anchorLinks = document.querySelectorAll('a[href^="#"]');

            anchorLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Optimize external links
            const externalLinks = document.querySelectorAll('a[target="_blank"]');
            externalLinks.forEach(function(link) {
                if (!link.hasAttribute('rel')) {
                    link.setAttribute('rel', 'noopener noreferrer');
                }
            });

            // Preload critical resources
            const criticalLinks = [
                'qcscore.html',
                'qcGPT.html'
            ];

            criticalLinks.forEach(function(href) {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = href;
                document.head.appendChild(link);
            });
        });

        // Intersection Observer for performance optimization
        if ('IntersectionObserver' in window) {
            const cardObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px 0px'
            });

            // Observe feature cards
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(function(card) {
                cardObserver.observe(card);
            });
        }

        // Performance monitoring
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver(function(list) {
                    const entries = list.getEntries();
                    entries.forEach(function(entry) {
                        if (entry.name === 'layout-shift' && entry.value > 0.1) {
                            console.warn('Layout shift detected:', entry.value);
                        }
                    });
                });

                observer.observe({ entryTypes: ['layout-shift'] });
            } catch (e) {
                // Silently fail if layout-shift is not supported
            }
        }

        // Font loading optimization
        if ('fonts' in document) {
            document.fonts.ready.then(function() {
                document.body.classList.add('fonts-loaded');
            });
        }

        // Error handling for images
        const images = document.querySelectorAll('img');
        images.forEach(function(img) {
            img.addEventListener('error', function() {
                this.style.display = 'none';
            });
        });

    })();
    </script>

</body>
</html>