<!doctype html>
<html class="no-js" lang="ar">
    <head>
        	<?php include 'header.php'; ?>

        <meta charset="utf-8">
        <title>هل نستطيع أن نحزر إلى أي شيء تلتفت عيناك ؟</title>
	<meta http-equiv="x-ua-compatible" content="ie=edge">
	<meta name="description" content="حلل شخصيتك الآن، ماذا تنتظر؟!">
    <meta name="author" content="" />
    <meta property="og:title" content="هل نستطيع أن نحزر إلى أي شيء تلتفت عيناك ؟" />
    <meta property="og:description" content="حلل شخصيتك الآن، ماذا تنتظر؟!" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="/" />
    <meta property="og:image:type" content="image/jpeg" />

    
<?php
$Tags = <<<"EOD"
<meta itemprop="image" content="XXXX1">
<link rel="image_src" href="XXXX2" />
<meta name="twitter:image" content="XXXX3">
<meta property="og:image" content="XXXX4" />

EOD;
$actual_link = 'https://' . $_SERVER['HTTP_HOST'];

$Tags2 = $Tags;

$Tags2 = str_replace("XXXX1",$actual_link . "/indeximg/10.jpg",$Tags2);
$Tags2 = str_replace("XXXX2",$actual_link . "/indeximg/10.jpg",$Tags2);
$Tags2 = str_replace("XXXX3",$actual_link . "/indeximg/10.jpg"  . "?" .uniqid(),$Tags2);
$Tags2 = str_replace("XXXX4",$actual_link . "/indeximg/10.jpg",$Tags2);
echo $Tags2;
?>

	<meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@MyyQuizCom">
    <meta name="twitter:title" content="هل نستطيع أن نحزر إلى أي شيء تلتفت عيناك ؟">
    <meta name="twitter:description" content="حلل شخصيتك الآن، ماذا تنتظر؟!">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="../../css/bootstrap.min.css"> 
        <link rel="stylesheet" href="../../css/animate.min.css">
        <link rel="stylesheet" href="../../css/style.css">
        <link rel="stylesheet" href="../../css/font-awesome.min.css">
		<link rel="stylesheet" href="../../css/slicknav.css">
        <link rel="shortcut icon" type="image/png" href="../../favicon.png"/>
		
		<!-- Head -->
		<script src="https://cdn.onesignal.com/sdks/OneSignalSDK.js" async=""></script>
<script>
  var OneSignal = window.OneSignal || [];
  OneSignal.push(function() {
    OneSignal.init({
      appId: "************************************",
    });
  });
</script>
    </head>
    <body>
        <!--[if lte IE 9]>
            <p class="browserupgrade">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade your browser</a> to improve your experience and security.</p>
        <![endif]-->
        <!-- header-start -->
        <header>
            <div class="header-area ">
                <div id="sticky-header" class="main-header-area">
                    <div class="container-fluid">
                        <div class="row align-items-center">
                            <div class="col-xl-3 col-lg-2">
                                <div class="logo">
                                    <a href="../../index.php"> <img src="../../img/logo.png" alt=""> </a>
                                </div>
                            </div>
                            <div class="col-xl-6 col-lg-7">
                                <div class="main-menu  d-none d-lg-block">
                                    <nav>
                                        <ul id="navigation">
                                            <li>
                                                <a class="active" href="../../index.php">الرئيسية</a>
                                            </li>
                                                   <li>
                                            <script>
                                            var rnd = Math.floor(Math.random() * 30) + 1;
                                            var out = `<a href="../../id/X/">اختبار عشوائي</a>`
                                            var res = out.replace("X", rnd);
                                            document.write(res);
                                            </script>
                                            </li>
                                     <li>
                                                <a href="../../makequiz.php">صمم اختبارك ✨ </a>
                                            </li>
                                            <li>
                                                <a href="../../privacy.html">سياسة الخصوصية</a>
                                            </li>
                                            <li>
                                                <a href="../../contact.html">اتصل بنا</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mobile_menu d-block d-lg-none"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <!-- header-end -->
                    <!-- bradcam_area  -->
			

        <div class="bradcam_area bradcam_bg_1">
            <div class="container">
                <div class="row">
                    <div class="col-xl-12">
                        <div class="bradcam_text">
                            <h3 class="text-center">هل نستطيع أن نحزر إلى أي شيء تلتفت عيناك ؟</h3>
                        </div>
                    </div>
					
                </div>
            </div>
        </div>
        <!-- /bradcam_area  -->
        <!-- Start Sample Area -->
        <div w3-include-html="../../sda.html" class="section_title text-center"></div>
        <section class="sample-text-area">
            <div class="box_1170 container text-right" id="boxQueiz"> 
                <h1 class="text-center text-heading">هل نستطيع أن نحزر إلى أي شيء تلتفت عيناك ؟</h1>
                <h3 style="display: none;" id="TxtRandomNUmber"></h3>
                <script>
function includeHTML() {
  var z, i, elmnt, file, xhttp;
  /*loop through a collection of all HTML elements:*/
  z = document.getElementsByTagName("*");
  for (i = 0; i < z.length; i++) {
    elmnt = z[i];
    /*search for elements with a certain atrribute:*/
    file = elmnt.getAttribute("w3-include-html");
    if (file) {
      /*make an HTTP request using the attribute value as the file name:*/
      xhttp = new XMLHttpRequest();
      xhttp.onreadystatechange = function() {
        if (this.readyState == 4) {
          if (this.status == 200) {elmnt.innerHTML = this.responseText;}
          if (this.status == 404) {elmnt.innerHTML = "Page not found.";}
          /*remove the attribute, and call this function once more:*/
          elmnt.removeAttribute("w3-include-html");
          includeHTML();
        }
      }      
      xhttp.open("GET", file, true);
      xhttp.send();
      /*exit the function:*/
      return;
    }
  }
};
</script>
                <script>
includeHTML();
</script>
                <script>
document.getElementById("TxtRandomNUmber").innerHTML =
Math.floor(Math.random() * 100) + 1;
</script>
                <script>

for(var questionArray=[["\u0647\u0644 \u0623\u0646\u062A \u0645\u062A\u0641\u0627\u0626\u0644 \u061F","\u0646\u0639\u0645 \u060C \u0643\u062B\u064A\u0631\u0627\u064B","\u0644\u0627 \u060C \u0623\u0628\u062F\u0627\u064B ","\u0642\u0644\u064A\u0644\u0627\u064B "],["\u0647\u0644 \u062A\u0634\u0639\u0631 \u0623\u0646\u0643 \u062D\u0642\u0642\u062A \u0627\u0644\u0643\u062B\u064A\u0631 \u0645\u0646 \u0623\u062D\u0644\u0627\u0645\u0643 \u061F","\u0646\u0639\u0645","\u0644\u0627","\u062D\u0642\u0642\u062A \u0627\u0644\u0642\u0644\u064A\u0644 \u0641\u0642\u0637 \u0645\u0646 \u0623\u062D\u0644\u0627\u0645\u064A "],["\u0628\u0631\u0623\u064A\u0643 \u0623\u064A\u0647\u0645\u0627 \u0623\u0641\u0636\u0644 \u0645\u0627\u0636\u064A\u0643 \u0623\u0645 \u062D\u0627\u0636\u0631\u0643 \u061F","\u0627\u0644\u0645\u0627\u0636\u064A","\u0627\u0644\u062D\u0627\u0636\u0631","\u0643\u0644\u0627\u0647\u0645\u0627 \u0633\u064A\u0621"]],i=0;i<questionArray.length;i++){document.write("<form><div id='container-"+i+"' style='display:none'><span class='question step-"+i+"'>"+questionArray[i][0]+"</span>");for(var x=1;4>x;x++)document.write("<br><input type='radio' style='margin: 10px 10px 10px 10px'  class='answer'         name='answer' value='"+questionArray[i][x]+"  '>"+questionArray[i][x]+"");var nowQuestion=i+2,btnText="\u0627\u0644\u0625\u0646\u062A\u0642\u0627\u0644 \u0625\u0644\u0649 \u0627\u0644\u0633\u0624\u0627\u0644 "+nowQuestion+" \u0645\u0646 \u0623\u0635\u0644 "+questionArray.length;i<questionArray.length-1?(buttonText=btnText,document.write("<br/><br/><button id='question-"+i+" ' class='genric-btn success' onclick='next(event)'>"+buttonText+"</button>")):(buttonText="\u0627\u0644\u062D\u0635\u0648\u0644 \u0639\u0644\u0649 \u0627\u0644\u0646\u062A\u064A\u062C\u0629",document.write("<br/><br/><button id='Submit' class='genric-btn success' onclick='event.preventDefault(); ssUbmit();'>"+buttonText+"</button>")),document.write("</div>"),document.write("</form>")}function ssUbmit(a){if(resultT===void 0){document.getElementById("undertextSpace").innerHTML="\u0644\u0642\u062F \u0633\u062D\u0628\u062A \u0639\u0644\u0649 \u0648\u0627\u062D\u062F \u0645\u0646 \u0627\u0644\u0627\u0633\u0626\u0644\u0629!";var b=questionArray.length-1;document.getElementById("container-"+b).style.display="none";var c=document.getElementById("resuktTextBoxX").innerHTML,d=c.replace("XXXXXX","\u0643\u0641 \u0639\u0644\u0649 \u0648\u062C\u0647\u0643");document.getElementById("resuktTextBoxX").innerHTML=d;var b=questionArray.length-1;document.getElementById("container-"+b).style.display="none",document.getElementById("boxQueiz").style.display="none",document.getElementById("hideenResult").style.display="block",document.getElementById("imagesSrc").src="../../images/tenor.gif"}else{var e=resultT,f=e.split("|");document.getElementById("undertextSpace").innerHTML=f[1];var c=document.getElementById("resuktTextBoxX").innerHTML,d=c.replace("XXXXXX",f[0]);document.getElementById("resuktTextBoxX").innerHTML=d;var b=questionArray.length-1;document.getElementById("container-"+b).style.display="none",document.getElementById("boxQueiz").style.display="none",document.getElementById("hideenResult").style.display="block",document.getElementById("imagesSrc").src=f[2],document.getElementById("Titly").value=document.getElementById("resuktTextBoxX").innerHTML,document.getElementById("Picly").value=f[2],document.getElementById("Descpy").value=document.getElementById("undertextSpace").innerHTML,SubmitFormData.call(),a.preventDefault()}}count=0,document.getElementById("container-0").style.display="block";function next(a){a.preventDefault();var b=document.getElementById(a.target.id);b.parentNode.style.display="none",count++,document.getElementById("container-"+count).style.display="block"}for(var characterAnswer=[[0,0,0,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0646\u0627\u062C\u062D|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0646\u0627\u062C\u062D.|1585557144.jpg"],[0,0,1,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0631\u0639\u0628|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0631\u0639\u0628.|1585557184.jpg"],[1,1,1,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0639\u0642\u062F|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0639\u0642\u062F.|1585557170.jpg"],[0,1,2,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0635\u0639\u0628|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0635\u0639\u0628.|1585557123.jpg"],[1,1,2,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0646\u062A\u0647\u064A|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0645\u0646\u062A\u0647\u064A.|1585557065.jpg"],[1,2,2,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u0628\u062F\u0648\u0646 \u0645\u0644\u0627\u0645\u062D|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u0628\u062F\u0648\u0646 \u0645\u0644\u0627\u0645\u062D.|1585557221.jpg"],[2,2,2,"\u0623\u0646\u0627 \u0644\u062F\u064A \u0645\u0633\u062A\u0642\u0628\u0644 \u062D\u0627\u0641\u0644|\u0645\u0646 \u0627\u0644\u0648\u0627\u0636\u062D \u0623\u0646\u0643 \u062A\u0645\u062A\u0644\u0643 \u0645\u0633\u062A\u0642\u0628\u0644 \u062D\u0627\u0641\u0644.|1585557089.jpg"]],inputs=document.getElementsByTagName("input"),i=0;i<inputs.length;i++)inputs[i].addEventListener("click",check);var userAnswers=[];function check(){userAnswers=[];for(var a=0,b=0;b<inputs.length;b++)inputs[b].checked&&(userAnswers.push(b%3),a++);a==questionArray.length&&rate()}function rate(){console.log(userAnswers);for(var a=0;a<userAnswers.length;a++)for(var b=0;b<characterAnswer.length;b++){characterAnswer[b][4]=0;for(var c=0;4>c;c++)userAnswers[a]==characterAnswer[b][c]&&characterAnswer[b][4]++}answer()}function showResult(){for(var b,c=0,d=0;d<characterAnswer.length;d++)characterAnswer[d][4]>c&&(c=characterAnswer[d][4],b=characterAnswer[d][3])}var resultT;function answer(){for(var b,c=0,d=0;d<characterAnswer.length;d++)characterAnswer[d][4]>c&&(c=characterAnswer[d][4],b=characterAnswer[d][3]);resultT=b}

</script>
            </div>
            <div class="box_1170 container text-center" style="display: none" id="hideenResult">
                <p class="sample-text"></p>
                <p></p>
                <h4 class="text-heading" id="resuktTextBoxX" name="resuktTextBoxX">XXXXXX</h4>
                <img src="../../images/empty.gif" id="imagesSrc" class="responsive"/>
                <h4 class="sample-text" id="undertextSpace"></h4>
                <h3 class="sample-text text-danger" id="shares1">✨ الآن قم بمشاركة النتيجة مع اصدقائك لتعرفهم اكثر ✨</h3>
                <h3 class="sample-text text-danger" id="ghf">👇</h3>

<script>

var html = `
				
				<i class="lahh" style="font-size: 2rem;">👈</i>
				<a href="#" onClick="MyWindow=window.open('whatsapp://send?text=TitleOfPage  MYQUIZLINK','MyWindow','width=600,height=300'); return false;"><i class="fa fa-whatsapp" style="font-size: 3rem;"></i></a>
				<a href="#" onClick="MyWindow=window.open('http://twitter.com/share?text=TitleOfPage&url=MYQUIZLINK','MyWindow','width=600,height=300'); return false;"><i class="fa fa-twitter" style="font-size: 3rem;"></i></a>
				<a href="#" onClick="MyWindow=window.open('https://www.facebook.com/sharer/sharer.php?u=MYQUIZLINK&t=TitleOfPage','MyWindow','width=600,height=300'); return false;"><i class="fa fa-facebook" style="font-size: 3rem;"></i></a>
				<i class="lahh" style="font-size: 2rem;">👉</i>
				<br></br>
				<button id="myInput" class="genric-btn success" onclick="myCopy();">نسخ رابط النتيجة</button>

`
html = html.replace("MYQUIZLINK", window.location.href + "Meta" + document.getElementById("TxtRandomNUmber").innerHTML +".htm");
html = html.replace("TitleOfPage", document.title);
html = html.replace("MYQUIZLINK", window.location.href + "Meta" + document.getElementById("TxtRandomNUmber").innerHTML +".htm");
html = html.replace("TitleOfPage", document.title);
html = html.replace("MYQUIZLINK", window.location.href + "Meta" + document.getElementById("TxtRandomNUmber").innerHTML +".htm");
html = html.replace("MYQUIZLINK", window.location.href + "Meta" + document.getElementById("TxtRandomNUmber").innerHTML +".htm");
html = html.replace("TitleOfPage", document.title);

html = html.replace("index.php", "");
html = html.replace("index.php", "");
html = html.replace("index.php", "");
html = html.replace("index.php", "");

document.write(html);

  var linkMeta2 = window.location.href + "Meta" + document.getElementById("TxtRandomNUmber").innerHTML + ".htm";


 function myCopy() {
    var myCode = document.getElementById('myInput').value;
    var fullLink = document.createElement('input');
    document.body.appendChild(fullLink);
    fullLink.value = linkMeta2;
    fullLink.select();
    document.execCommand("copy", false);
    fullLink.remove();
	alert("تم النسخ!");
} 
</script>  
              <form id="HiddenSentData" name="HiddenSentData" method="post" onsubmit="return false" style="display: none">
                    <input type="text" id="Titly" name="Titly" value="John">
                    <input type="text" id="Picly" name="Picly" value="Doe">
                    <input type="text" id="Descpy" name="Descpy" value="Doe">
                    <input type="button" id="submitFormData" onclick="SubmitFormData(); return false" value="Submit"/>
                </form>
                <script>
function SubmitFormData(e) {

var Title = document.getElementById("resuktTextBoxX").innerHTML
var Desc = document.getElementById("undertextSpace").innerHTML
var Picy = document.getElementById("imagesSrc").src
var RndNum = document.getElementById("TxtRandomNUmber").innerHTML
var QuizURL = window.location.href
QuizURL = QuizURL.replace("index.php", "");
QuizURL = QuizURL.replace("indexBAK.php", "");
var QuizTitle = "أختبر نفسك الآن"

//alert(QuizURL);
//alert(3);

$.ajax({
	method: "POST",
	url: "submit.php",
    data : { "Titly": Title, "Descpy": Desc, "Picly": Picy, "RndNum": RndNum, "QuizURL": QuizURL, "QuizTitle": QuizTitle},
	})
  .done(function( msg ) {

	
  });
  }
</script>
            </div>
            <div class="container">
                <div w3-include-html="../../sda.html" class="section_title text-center"></div>
                <div class="row">
                    <div class="col-xl-12">
                        <div class="section_title text-center  wow fadeInUp" data-wow-duration=".7s" data-wow-delay=".5s">
                            <h3>اخترنا لك</h3>
                            <?php
                                
                                
                                $dataQuizOrignal = <<<"EOD"
                            <div class="block border mb-2 text-right" style="border-radius: 5px;">
                                <a href="../../id/LLLLLLLL/"><img src="../../indeximg/PPPPPPPP.jpg" border="0" width="120" height="80" style="padding: 5px;"></a>
                                <a href="../../id/LLLLLLLL/" style="position: absolute; margin: 15px auto;">TTTTTTTT</a>
                                <br>
                                <p></p>
                                <p></p>
                            </div>
                        
EOD;

    $file = "../../ids.txt";
    // Convert the text fle into array and get text of each line in each array index
    $file_arr = file($file);
    // Total number of linesin file
    $num_lines = count($file_arr);
    // Getting the last array index number by subtracting 1 as the array index starts from 0
    $last_arr_index = $num_lines - 1;
    // Random index number
    
    for ($i = 0; $i < 8; $i++) {
    $rand_index = rand(0, $last_arr_index);
    // random text from a line. The line will be a random number within the indexes of the array
    $rand_text = $file_arr[$rand_index];
    
     
$Slicers  = $rand_text;
$Slicers = explode("|", $Slicers);

$tempData = $dataQuizOrignal;

$tempData = str_replace("LLLLLLLL",$Slicers[0],$tempData);
$tempData = str_replace("TTTTTTTT",$Slicers[1],$tempData);
$tempData = str_replace("PPPPPPPP",$Slicers[2],$tempData);
echo $tempData;

}
  
    ?>
                        </div>
                    </div>
                </div>                 
        </section>
        <!-- End Sample Area -->
        <!-- Start Button -->
        <!-- End Button -->
        <!-- Start Align Area -->
        <div class="whole-wrap">
</div>
        <!-- End Align Area -->
        <!-- footer start -->
        <footer class="footer">
            <div class="copy-right_text">
                <div class="container">
                    <div class="row">
                        <div class="col-xl-12">
                            <p class="copy_right text-center">جميع الحقوق محفوظة</p>
								<?php
	function _bot_detected() {
  return (
    isset($_SERVER['HTTP_USER_AGENT'])
    && preg_match('/bot|crawl|slurp|spider|facebook|twitter|yandex|mediapartners/i', $_SERVER['HTTP_USER_AGENT'])
  );
}

if(_bot_detected()){
  echo 
"AAAAA"
;
}
?>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!--/ footer end  -->
			        <!-- JS here -->
            <script src="../../js/vendor/jquery-1.12.4.min.js"></script>
            <script src="../../js/bootstrap.min.js"></script>
            <script src="../../js/owl.carousel.min.js"></script>
            <script src="../../js/isotope.pkgd.min.js"></script>
            <script src="../../js/wow.min.js"></script>
            <script src="../../js/jquery.slicknav.min.js"></script>
            <!--contact js-->
            <script src="../../js/jquery.form.js"></script>
            <script src="../../js/jquery.validate.min.js"></script>
            <script src="../../js/main.js"></script>
    </body>
