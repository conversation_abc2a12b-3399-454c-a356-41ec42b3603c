<?php if ($skipHero !== 1): ?>
<?php if ($heroImg): ?>
<!-- Preload hero image -->
<link rel="preload" as="image" href="<?= htmlspecialchars($heroImg) ?>" fetchpriority="high">
<?php endif; ?>


<style>
@font-face {
  font-family: 'YourFont';
  src: url('/fonts/YourFont.woff2') format('woff2');
  font-display: swap;
}

.hero-section {
  position: relative;
  width: 100%;
  height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #2563eb, #60a5fa);
  overflow: hidden;
}

.hero-bg-container {
  position: absolute;
  top: 0; right: 0; bottom: 0; left: 0;
}

.hero-image {
  position: absolute;
  top: 0; right: 0; bottom: 0; left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.hero-image.loaded {
  opacity: 1;
}

.hero-content {
  position: relative;
  z-index: 10;
  max-width: 80rem;
  padding: 5rem 1rem;
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  text-decoration: none;
  font-weight: 600;
  min-width: 120px;
  text-align: center;
}

.hero-btn-primary {
  background-color: white;
  color: #2563eb;
}

.hero-btn-secondary {
  border: 2px solid white;
  color: white;
}

@keyframes gentle-bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.scroll-hint {
  animation: gentle-bounce 2s infinite;
  position: absolute;
  bottom: 2rem; /* bottom-8 */
  left: 50%; /* left-1/2 */
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  opacity: 0.75;
  transition: opacity 0.3s;
  width: 2rem; /* w-[2rem] */
  height: 3rem; /* h-[3rem] */
}

.scroll-hint:hover {
  opacity: 1;
}

.scroll-hint-text {
  display: block;
  margin-bottom: 0.25rem; /* mb-1 */
  font-size: 0.875rem; /* text-sm */
  line-height: 1; /* leading-none */
  font-family: 'YourFont', sans-serif;
      width: max-content;
}

.scroll-hint svg {
  width: 1.5rem; /* w-6 */
  height: 1.5rem; /* h-6 */
  flex-shrink: 0;
}
</style>

<header class="hero-section">
  <div class="hero-bg-container">
    <?php if ($heroImg): ?>
    <!-- Image has fixed dimensions to prevent CLS -->
    <img
      src="<?= htmlspecialchars($heroImg) ?>"
      alt="<?= htmlspecialchars($heroImgAlt) ?>"
      class="hero-image"
      loading="eager"
      width="1920"
      height="1080"
      decoding="async"
      fetchpriority="high"
      onload="this.classList.add('loaded')"
    >
    <?php endif; ?>
  </div>

  <div class="hero-content">
    <h1 class="hero-title"><?= htmlspecialchars($heroMainText) ?></h1>
    <p class="hero-subtitle"><?= htmlspecialchars($heroSubText) ?></p>

    <div class="hero-buttons">
      <?php if ($heroConfig['right']['enabled']) : ?>
      <a href="<?= htmlspecialchars($heroConfig['right']['link']) ?>"
         class="hero-btn hero-btn-primary"
         aria-label="<?= htmlspecialchars($heroConfig['right']['text']) ?>">
        <?= htmlspecialchars($heroConfig['right']['text']) ?>
      </a>
      <?php endif; ?>

      <?php if ($heroConfig['left']['enabled']) : ?>
      <a href="<?= htmlspecialchars($heroConfig['left']['link']) ?>"
         class="hero-btn hero-btn-secondary"
         aria-label="<?= htmlspecialchars($heroConfig['left']['text']) ?>">
        <?= htmlspecialchars($heroConfig['left']['text']) ?>
      </a>
      <?php endif; ?>
    </div>
  </div>

  <!-- Scroll hint with fixed size -->
  <div class="scroll-hint">
    <span class="scroll-hint-text">انزل تحت</span>
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
         stroke="currentColor" stroke-width="2" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
    </svg>
  </div>
</header>
<?php endif; ?>
