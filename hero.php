<?php if ($skipHero !== 1): ?>
<!-- Hero Section - Optimized for CLS and Performance -->
<?php if ($heroImg): ?>
<link rel="preload" as="image" href="<?= htmlspecialchars($heroImg) ?>" fetchpriority="high">
<?php endif; ?>
<style>
/* Critical CSS for hero section to prevent CLS */
.hero-section {
  position: relative;
  width: 100vw;
  width: 100%;
  height: 100vh;
  height: 100dvh;
  min-height: 100vh;
  min-height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #2563eb, #60a5fa);
  overflow: hidden;
  contain: layout style paint;
  isolation: isolate;
}

.hero-bg-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(37, 99, 235, 0.8), rgba(96, 165, 250, 0.8));
  contain: layout style paint;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  will-change: opacity;
  contain: layout style paint;
}

.hero-image.loaded {
  opacity: 1;
}

.hero-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 80rem;
  margin: 0 auto;
  padding: 5rem 1rem;
  text-align: center;
  color: white;
  contain: layout style;
  will-change: auto;
}

.hero-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-btn {
  padding: 0.75rem 2rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  font-weight: 600;
  min-width: 120px;
  text-align: center;
}

.hero-btn-primary {
  background-color: white;
  color: #2563eb;
}

.hero-btn-primary:hover {
  background-color: #eff6ff;
  transform: scale(1.05);
}

.hero-btn-secondary {
  border: 2px solid white;
  color: white;
}

.hero-btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 3.75rem;
  }
  .hero-subtitle {
    font-size: 1.25rem;
  }
}
</style>

<header class="hero-section">
  <!-- Gradient + Image Container -->
  <div class="hero-bg-container">
    <?php if ($heroImg): ?>
    <img
      src="<?= htmlspecialchars($heroImg) ?>"
      alt="<?= htmlspecialchars($heroImgAlt) ?>"
      class="hero-image"
      loading="eager"
      width="1920"
      height="1080"
      decoding="async"
      fetchpriority="high"
      onload="this.classList.add('loaded')"
    >
    <?php endif; ?>
  </div>

  <!-- Content Container -->
  <div class="hero-content">
    <h1 class="hero-title">
      <?= htmlspecialchars($heroMainText) ?>
    </h1>
    <p class="hero-subtitle">
      <?= htmlspecialchars($heroSubText) ?>
    </p>

    <div class="hero-buttons">
      <?php if ($heroConfig['right']['enabled']) : ?>
      <a href="<?= htmlspecialchars($heroConfig['right']['link']) ?>"
         class="hero-btn hero-btn-primary"
         aria-label="<?= htmlspecialchars($heroConfig['right']['text']) ?>">
        <?= htmlspecialchars($heroConfig['right']['text']) ?>
      </a>
      <?php endif; ?>

      <?php if ($heroConfig['left']['enabled']) : ?>
      <a href="<?= htmlspecialchars($heroConfig['left']['link']) ?>"
         class="hero-btn hero-btn-secondary"
         aria-label="<?= htmlspecialchars($heroConfig['left']['text']) ?>">
        <?= htmlspecialchars($heroConfig['left']['text']) ?>
      </a>
      <?php endif; ?>
    </div>
  </div>

  <!-- Scroll hint - Fixed positioning -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-white opacity-75 hover:opacity-100 transition-opacity duration-300">
    <style>
    @keyframes gentle-bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }
    .scroll-hint {
      animation: gentle-bounce 2s infinite;
    }
    </style>
    <div class="scroll-hint">
      <span class="block mb-1 text-sm">انزل تحت</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" aria-hidden="true">
        <path stroke-linecap="round" stroke-linejoin="round" d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  </div>
</header>
<?php endif; ?>