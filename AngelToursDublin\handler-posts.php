<?php


function isumail($email = ""){
			if(preg_match("/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,4})$/",$email)){			
				return true;
			}
			return false;
		}

function isuname($name =  ""){
			if (preg_match("/^[a-z]{1,}(\'){0,1}[a-z]{1,}$/i", $name)) {
				return true;
		
			}
			else {
				return false;
			}
		}

if (isset($_POST['action']) && $_POST['action'] == 'submitted') {
	
	$cMail = trim($_POST['uemail']);

	$cfName = trim($_POST['ufname']);
	
	$clName = trim($_POST['ulname']);
	
	$cTel = trim($_POST['utel']);

	$cHotel = trim($_POST['uhotel']);

	$cCity = trim($_POST['ucity']);
	
	$cStudent = trim($_POST['student']);

	$cKids = trim($_POST['kids']);
	
	$cAdult = trim($_POST['adult']);

	$cMessage = trim($_POST['message']);

	$isfName=isuname($cfName);
	$islName=isuname($clName);
	$isMail=isumail($cMail);
	$isCity=$cCity;
	
	if ((!$isMail)||(!$isfName)||(!$islName)||($isCity=="")){

	require_once("book.php");	

	}

	else {	
	
	$myMessage= "Name: " . $cfName. " ".$clName ."\n"."\n";
	$myMessage.= "Mobile: " .$cTel."\n"."\n";
	$myMessage.= "Hotel/Appartment: " .$cHotel."\n"."\n";
	$myMessage.= "I Want To do Tours: " .$cCity."\n"."\n";
	$myMessage.= "Adult: " .$cAdult." Student: " .$cStudent.             " Kids: " .$cKids."\n"."\n";
	$myMessage.= "Message: " .$cMessage."\n"."\n";

	$to      = '<EMAIL>';
	$subject = 'New Booking AngelTours';
	$message = $myMessage;
	$headers = 'From: '.$cMail. "\r\n" .
	    'Reply-To:'.$cMail. "\r\n" .
	    'X-Mailer: PHP/' . phpversion();
	mail($to, $subject, $message, $headers);

	require_once("thanks.php");	

	}
	
}

?> 