<?php
$Titly = $Picly = $Descpy = $RndNum = $QuizURL = $QuizTitle = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
  $Titly = test_input($_POST["Titly"]);
  $Picly = test_input($_POST["Picly"]);
  $Descpy = test_input($_POST["Descpy"]);
  $RndNum = test_input($_POST["RndNum"]);
  $QuizURL = test_input($_POST["QuizURL"]);
  $QuizTitle = test_input($_POST["QuizTitle"]);
}

if ($_SERVER["REQUEST_METHOD"] == "GET") {
die;
}


function test_input($data) {
  $data = trim($data);
  $data = stripslashes($data);
  $data = htmlspecialchars($data);
  return $data;
}

$NewMetaFile = <<<"EOD"
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">  
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=no" name="viewport" />
<meta name="description" content="DesciptonIsHere" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:site" content="@MyyQuizCom" />
<meta name="twitter:title" content="ResultIsHere" />
<meta name="twitter:description" content="DesciptonIsHere" />
<meta property="og:title" content="ResultIsHere" />
<meta property="og:url" content="URLisHere" />
<meta property="og:description" content="DesciptonIsHere" />
<meta property="og:site_name" content="MyyQuizCom" />
<meta property="og:image" content="ImageIsHere" />
<meta property="og:image:type" content="image/jpeg" />
<meta name="theme-color" content="#FFFF00"><meta name="twitter:image" content="ImageIsHere" />
<script>window.location.replace('URLisHere');</script>
</head>
</html>
EOD;

$NewMetaFile2 = $NewMetaFile;

$NewMetaFile2 = str_replace("ResultIsHere",$Titly,$NewMetaFile2);
$NewMetaFile2 = str_replace("DesciptonIsHere",$QuizTitle,$NewMetaFile2);
$NewMetaFile2 = str_replace("ImageIsHere",$Picly,$NewMetaFile2);
$NewMetaFile2 = str_replace("URLisHere",$QuizURL,$NewMetaFile2);
echo $NewMetaFile2;

$myfile = fopen("Meta" . $RndNum . ".htm", "w") or die("Unable to open file!");
fwrite($myfile, $NewMetaFile2);
fclose($myfile);
?>