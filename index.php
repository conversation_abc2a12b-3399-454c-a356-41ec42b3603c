<?php
require_once 'db.php';

// Get latest quizzes
$latestQuery = $pdo->query("
    SELECT id, title, description, image 
    FROM quizzes 
	WHERE `v` = 1
    ORDER BY id DESC 
    LIMIT 27
");
$latestQuizzes = $latestQuery->fetchAll();

// Get popular quizzes
$popularQuery = $pdo->query("
    SELECT ur.quiz_id, COUNT(*) AS quiz_count, q.title, q.description, q.image
    FROM user_results ur
    JOIN quizzes q ON ur.quiz_id = q.id
	WHERE  q.v = 1
    AND	   ur.created_at >= NOW() - INTERVAL 1 DAY
    GROUP BY ur.quiz_id
    ORDER BY quiz_count DESC
    LIMIT 15
");
$popularQuizzes = $popularQuery->fetchAll();

//print_r($popularQuizzes);

// Helper function to truncate description
function truncateDescription($text, $length) {
    if (mb_strlen($text, 'UTF-8') > $length) {
        return mb_substr($text, 0, $length, 'UTF-8') . '...';
    }
    return $text;
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماي كويز</title>
    
    <!-- SEO Meta Tags -->
    <meta name="author" content="YouName">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://myyquiz.com">
    <meta property="og:title" content="ماي كويز">
    <meta property="og:description" content="استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!">
	<meta property="og:image" content="https://myyquiz.com/images/bnr.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://myyquiz.com">
    <meta property="twitter:title" content="ماي كويز">
    <meta property="twitter:description" content="استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!">
    <meta property="twitter:image" content="https://myyquiz.com/images/bnr.jpg">
    <meta name="twitter:site" content="@MyyQuizCom">
	<meta name="twitter:image:alt" content="ماي كويز Myyquiz">

    <!-- IDK 
	Remember all <a href=" was with /
	-->
	<meta itemprop="image" content="https://myyquiz.com/images/bnr.jpg">
	<link rel="image_src" href="https://myyquiz.com/images/bnr.jpg" />

	<?php include 'header.php'; ?>

</head>
<body class="bg-gray-50">

    <!-- Navbar -->
	<?php include 'navbar.php'; ?>

    <!-- Hero Section -->
	<?php 
	$skipHero = 0;
	$heroImg = "";
	$heroImgAlt = "";
	$heroMainText = "ماي كويز | Myyquiz";
	$heroSubText = "منصة اختبارات ترفيهيه قصيرة مصممة بعناية لدراسة وتحليل شخصيتك ✨";
	
	
	// In your config/header.php
	$heroConfig = [
		'left' => [
			'link' => '/make.php',
			'text' => 'صمم اختبارك ✨',
			'enabled' => true
		],
		'right' => [
			'link' => '#',
			'text' => '⬇️',
			'enabled' => true
		]
	];

	include 'hero.php'; 
	?>


    <!-- Features Section -->
    <section class="py-20 bg-white-50 bg-white-800">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center text-primary-DEFAULT text-blue-400 mb-12">احدث الاختبارات</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				<!-- Feature 1 -->				
			<!-- Latest Quizzes Section -->
				<?php foreach ($latestQuizzes as $quiz): ?>
				<a href="quiz/<?= htmlspecialchars($quiz['id']) ?>" class="block">
					<div class="bg-white bg-gray-700 rounded-lg shadow-md p-8 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-lg">
						<img 
							src="<?= htmlspecialchars(str_replace('/imagesindex/', '/imageshq/', $quiz['image'])) ?>"
							alt="<?= htmlspecialchars($quiz['title']) ?>"
							width="416"
							height="233"
							loading="lazy"
							class="rounded-lg"
						>
						<h3 class="text-xl font-semibold mb-3"><?= htmlspecialchars($quiz['title']) ?></h3>
						<p class="text-gray-600 text-gray-300"><?= htmlspecialchars(truncateDescription($quiz['description'], 86)) ?></p>
						<span class="border-2 border-green-500 text-green-500 font-semibold py-2 px-20 rounded-lg hover:bg-green-500 hover:text-white transition-all duration-300 inline-block">
							إبدأ
						</span>
					</div>
				</a>
				<?php endforeach; ?>
			</div> 
		</div> 
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white-50 bg-white-800">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center text-primary-DEFAULT text-blue-400 mb-12">التريند</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				<!-- Feature 1 -->				
			<!-- Latest Quizzes Section -->
				<?php foreach ($popularQuizzes as $quiz): ?>
				<a href="quiz/<?= htmlspecialchars($quiz['quiz_id']) ?>" class="block">
					<div class="bg-white bg-gray-700 rounded-lg shadow-md p-8 text-center transition-all duration-300 hover:-translate-y-2 hover:shadow-lg">
						<img 
							src="<?= htmlspecialchars(str_replace('/imagesindex/', '/imageshq/', $quiz['image'])) ?>"
							alt="<?= htmlspecialchars($quiz['title']) ?>"
							width="416px"
							height="233px"
							loading="lazy"
							class="h-auto max-w-full rounded-lg"
						>
						<h3 class="text-xl font-semibold mb-3"><?= htmlspecialchars($quiz['title']) ?></h3>
						<p class="text-gray-600 text-gray-300"><?= htmlspecialchars(truncateDescription($quiz['description'], 86)) ?></p>
						<span class="border-2 border-green-500 text-green-500 font-semibold py-2 px-20 rounded-lg hover:bg-green-500 hover:text-white transition-all duration-300 inline-block">
							إبدأ
						</span>
					</div>
				</a>
				<?php endforeach; ?>
			</div> 
		</div> 
				
    </section>

    <!-- Footer -->
	<?php include 'footer.php'; ?>

</body>
</html>