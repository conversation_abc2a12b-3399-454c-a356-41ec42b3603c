<?php
require_once 'db.php';

// Optimized database queries with prepared statements and indexing hints
try {
    // Get latest quizzes - optimized query
    $latestQuery = $pdo->prepare("
        SELECT id, title, description, image
        FROM quizzes
        WHERE v = 1
        ORDER BY id DESC
        LIMIT 27
    ");
    $latestQuery->execute();
    $latestQuizzes = $latestQuery->fetchAll(PDO::FETCH_ASSOC);

    // Get popular quizzes - optimized query with better indexing
    $popularQuery = $pdo->prepare("
        SELECT ur.quiz_id, COUNT(*) AS quiz_count, q.title, q.description, q.image
        FROM user_results ur
        INNER JOIN quizzes q ON ur.quiz_id = q.id
        WHERE q.v = 1
        AND ur.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        GROUP BY ur.quiz_id, q.title, q.description, q.image
        ORDER BY quiz_count DESC
        LIMIT 15
    ");
    $popularQuery->execute();
    $popularQuizzes = $popularQuery->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Fallback to empty arrays if database fails
    $latestQuizzes = [];
    $popularQuizzes = [];
    error_log("Database error in index.php: " . $e->getMessage());
}

//print_r($popularQuizzes);

// Helper function to truncate description
function truncateDescription($text, $length) {
    if (mb_strlen($text, 'UTF-8') > $length) {
        return mb_substr($text, 0, $length, 'UTF-8') . '...';
    }
    return $text;
}

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماي كويز</title>

    <!-- Performance optimization meta tags -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="theme-color" content="#ffffff">

    <!-- Resource hints for better performance -->
    <link rel="preconnect" href="https://myyquiz.com">
    <link rel="dns-prefetch" href="https://myyquiz.com">

    <!-- SEO Meta Tags -->
    <meta name="author" content="YouName">
    <meta name="description" content="استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://myyquiz.com">
    <meta property="og:title" content="ماي كويز">
    <meta property="og:description" content="استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!">
	<meta property="og:image" content="https://myyquiz.com/images/bnr.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://myyquiz.com">
    <meta property="twitter:title" content="ماي كويز">
    <meta property="twitter:description" content="استكشف أسرار شخصيتك واختبر مهاراتك عبر مجموعة اختبارات شيقة ومتنوعة، تمتع بتجربة فريدة مع نتائج دقيقة تمنحك معرفة عميقة ووقتًا مليئًا بالتشويق!">
    <meta property="twitter:image" content="https://myyquiz.com/images/bnr.jpg">
    <meta name="twitter:site" content="@MyyQuizCom">
	<meta name="twitter:image:alt" content="ماي كويز Myyquiz">

    <!-- IDK 
	Remember all <a href=" was with /
	-->
	<meta itemprop="image" content="https://myyquiz.com/images/bnr.jpg">
	<link rel="image_src" href="https://myyquiz.com/images/bnr.jpg" />

	<?php include 'header.php'; ?>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "ماي كويز",
        "url": "https://myyquiz.com",
        "description": "منصة اختبارات ترفيهيه قصيرة مصممة بعناية لدراسة وتحليل شخصيتك",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://myyquiz.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>

    <!-- Critical CSS for CLS Prevention -->
    <style>
        /* Critical styles for faster initial render and CLS prevention */
        body {
            background-color: #f9fafb;
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        /* Stable container dimensions */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
        }

        /* Stable grid layout to prevent shifts */
        .grid {
            display: grid;
            gap: 2rem;
            width: 100%;
            contain: layout style;
        }
        @media (min-width: 768px) {
            .grid { grid-template-columns: repeat(2, 1fr); }
        }
        @media (min-width: 1024px) {
            .grid { grid-template-columns: repeat(3, 1fr); }
        }

        /* Quiz card with fixed dimensions to prevent CLS */
        .quiz-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            contain: layout style;
            will-change: transform;
        }

        .quiz-card:hover {
            transform: translateY(-0.5rem);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        /* Image container with fixed aspect ratio */
        .quiz-image {
            width: 100%;
            height: 231px;
            object-fit: cover;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            background-color: #f3f4f6;
        }

        /* Text content with stable heights */
        .quiz-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            line-height: 1.4;
            min-height: 2.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quiz-description {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.5;
            min-height: 4.5rem;
            display: flex;
            align-items: center;
        }

        /* Button with fixed dimensions */
        .quiz-button {
            border: 2px solid #16a34a;
            color: #16a34a;
            font-weight: 600;
            padding: 0.5rem 5rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: inline-block;
            text-decoration: none;
            min-width: 200px;
            height: 44px;
            line-height: 28px;
            text-align: center;
        }

        .quiz-button:hover {
            background-color: #16a34a;
            color: white;
        }

        /* Section spacing to prevent shifts */
        .section {
            padding: 5rem 0;
            width: 100%;
        }

        .section-title {
            font-size: 1.875rem;
            font-weight: 700;
            text-align: center;
            color: #3b82f6;
            margin-bottom: 3rem;
            line-height: 1.2;
        }
    </style>

</head>
<body class="bg-gray-50">

    <!-- Navbar -->
	<?php include 'navbar.php'; ?>

    <!-- Hero Section -->
	<?php 
	$skipHero = 0;
	$heroImg = "";
	$heroImgAlt = "";
	$heroMainText = "ماي كويز | Myyquiz";
	$heroSubText = "منصة اختبارات ترفيهيه قصيرة مصممة بعناية لدراسة وتحليل شخصيتك ✨";
	
	
	// In your config/header.php
	$heroConfig = [
		'left' => [
			'link' => '/make.php',
			'text' => 'صمم اختبارك ✨',
			'enabled' => true
		],
		'right' => [
			'link' => '#',
			'text' => '⬇️',
			'enabled' => true
		]
	];

	include 'hero.php'; 
	?>


    <!-- Features Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">احدث الاختبارات</h2>

            <div class="grid">
				<?php foreach ($latestQuizzes as $index => $quiz): ?>
				<a href="quiz/<?= htmlspecialchars($quiz['id']) ?>" class="block" aria-label="اختبار: <?= htmlspecialchars($quiz['title']) ?>">
					<div class="quiz-card">
						<img
							src="<?= htmlspecialchars(str_replace('/imagesindex/', '/imageshq/', $quiz['image'])) ?>"
							alt="<?= htmlspecialchars($quiz['title']) ?>"
							width="416"
							height="200"
							loading="<?= $index < 6 ? 'eager' : 'lazy' ?>"
							decoding="async"
							fetchpriority="<?= $index < 3 ? 'high' : 'low' ?>"
							class="quiz-image"
						>
						<h3 class="quiz-title"><?= htmlspecialchars($quiz['title']) ?></h3>
						<p class="quiz-description"><?= htmlspecialchars(truncateDescription($quiz['description'], 86)) ?></p>
						<span class="quiz-button">إبدأ</span>
					</div>
				</a>
				<?php endforeach; ?>
			</div>
		</div>
    </section>

    <!-- Popular Quizzes Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">التريند</h2>

            <div class="grid">
				<?php foreach ($popularQuizzes as $index => $quiz): ?>
				<a href="quiz/<?= htmlspecialchars($quiz['quiz_id']) ?>" class="block" aria-label="اختبار شائع: <?= htmlspecialchars($quiz['title']) ?>">
					<div class="quiz-card">
						<img
							src="<?= htmlspecialchars(str_replace('/imagesindex/', '/imageshq/', $quiz['image'])) ?>"
							alt="<?= htmlspecialchars($quiz['title']) ?>"
							width="416"
							height="231"
							loading="lazy"
							decoding="async"
							fetchpriority="low"
							class="quiz-image"
						>
						<h3 class="quiz-title"><?= htmlspecialchars($quiz['title']) ?></h3>
						<p class="quiz-description"><?= htmlspecialchars(truncateDescription($quiz['description'], 86)) ?></p>
						<span class="quiz-button">إبدأ</span>
					</div>
				</a>
				<?php endforeach; ?>
			</div>
		</div>
				
    </section>

    <!-- Footer -->
	<?php include 'footer.php'; ?>

</body>
</html>